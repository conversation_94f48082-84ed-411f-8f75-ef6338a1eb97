package com.morningstar.dataac.martgateway.core.datapointloader.entity;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.IDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.privatemodel.LakeHouseProperty;
import com.morningstar.dataac.martgateway.core.datapointloader.util.DataPointFiltersUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

@Slf4j
public class DataPointRepository {
    private static Map<String, DataPoint> dataPointMap = new HashMap<>();
    private static Set<String> subDataPointIdSet = new HashSet<>();
    private static Map<String, String> tscacheInternalIdMap = new HashMap<>();
    private static Map<String, DataPoint> masterHeaderDataPointMap = new HashMap<>();
    private static Map<String, String> dataGroupLocationMap = new HashMap<>();
    private static Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
    private static final Map<String, Pair<String, String>> dataSetDateDataPointMap = new HashMap<>();
    private static final Map<String, List<DataPoint>> groupRequiredDataPointMap = new HashMap<>();
    private static Map<String, Map<String, String>> rdsColDpsMap = new HashMap<>();
    private static Map<String, DataPointAggregate> dataPointAggregateMap = new HashMap<>();

    private DataPointRepository() {
        throw new IllegalStateException("DataPointRepository is static class");
    }

    public static DataPoint getByNid(String dpId) {
        return dataPointMap.getOrDefault(dpId, DataPoint.builder().id(dpId).build());
    }

    public static String getNidByTScacheInternalId(String id) {
        return tscacheInternalIdMap.get(id);
    }

    public static DataPoint getMasterHeaderDPById(String id) {
        return masterHeaderDataPointMap.get(id);
    }

    public static int getDataPointMapSize() {
        return dataPointMap.size();
    }

    public static boolean contain(String dpId) {
        return dataPointMap.containsKey(dpId) && !subDataPointIdSet.contains(dpId);
    }

    public static String getDataSource(String dataKeyPrefix) {
        return dataGroupLocationMap.getOrDefault(dataKeyPrefix, "cache");
    }

    public static List<DataPoint> getLakeHouseDataPointByProperty(LakeHouseProperty lakeHouseProperty){
        return DataPointFiltersUtil.queryByFilter(dataPointMap, DataPointFilter.builder().lakeHouseProperty(lakeHouseProperty).build());
    }

    public static Map<String, Pair<String, String>> getRdbDpsMapByGroupName(String groupName){
        return rdbGroupColDpsMap.get(groupName);
    }

    public static Map<String, String> getRdsDpsMapByGroupName(String groupName){
        return rdsColDpsMap.get(groupName);
    }

    public static Set<String> getRdbAllGroupNames() {
        return rdbGroupColDpsMap.keySet();
    }
    public static synchronized void setDataPointMap(Map<String, DataPoint> dataPointMap) {
        DataPointRepository.dataPointMap = dataPointMap;
    }

    public static synchronized Map<String, DataPoint> getDataPointMap() {
        return dataPointMap;
    }

    public static synchronized Set<String> getSubDataPointIdSet() {
        return DataPointRepository.subDataPointIdSet;
    }

    public static synchronized void setSubDataPointIdSet(Set<String> subDataPointIdSet) {
        DataPointRepository.subDataPointIdSet = subDataPointIdSet;
    }

    public static synchronized void setTscacheInternalIdMap(Map<String, String> tscacheInternalIdMap) {
        DataPointRepository.tscacheInternalIdMap = tscacheInternalIdMap;
    }

    public static synchronized void setMasterHeaderDataPointMap(Map<String, DataPoint> masterHeaderDataPointMap) {
        DataPointRepository.masterHeaderDataPointMap = masterHeaderDataPointMap;
    }

    public static synchronized void setDataGroupLocationMap(Map<String, String> dataGroupLocationMap) {
        DataPointRepository.dataGroupLocationMap = dataGroupLocationMap;
    }

    public static synchronized void setRdbGroupColDpsMap(Map<String, Map<String, Pair<String, String>>> rdbGroupColDpsMap) {
        DataPointRepository.rdbGroupColDpsMap = rdbGroupColDpsMap;
    }

    public static synchronized void addRdsGroupColDpsMap(Map<String, Map<String, String>> rdsColDpsMap) {
        DataPointRepository.rdsColDpsMap.putAll(rdsColDpsMap);
    }

    public static synchronized Map<String, Pair<String, String>> getDataSetDateDataPointMap() {
        return dataSetDateDataPointMap;
    }

    public static synchronized void addDataSetDateDataPointMap(String key, Pair<String,String> value) {
        DataPointRepository.dataSetDateDataPointMap.put(key,value);
    }

    public static synchronized void addGroupRequiredDataPoint(DataPoint dataPoint) {
        List<DataPoint> requiredDataPoints = groupRequiredDataPointMap.putIfAbsent(dataPoint.getGroupName(),
                new ArrayList<>(Collections.singletonList(dataPoint)));
        if (requiredDataPoints != null && !requiredDataPoints.contains(dataPoint)) {
            requiredDataPoints.add(dataPoint);
        }
    }

    public static List<DataPoint> getGroupRequiredDataPoint(String groupName) {
        return groupRequiredDataPointMap.get(groupName);
    }

    public static synchronized void setDataPointAggregateMap(Map<String, DataPointAggregate> dataPointAggregateMap) {
        DataPointRepository.dataPointAggregateMap = dataPointAggregateMap;
    }

    public static synchronized Map<String, DataPointAggregate> getDataPointAggregateMap() {
        return dataPointAggregateMap;
    }

    public static IDataPoint getDpByNid(String dpId, DataPointAggregate.SrcType srcType) {
        DataPointAggregate aggregate = dataPointAggregateMap.get(dpId);
        if (aggregate == null) {
            return null;
        }
        return aggregate.getSrcMap().get(srcType);
    }

    public static boolean dataPointAggregateMapContain(String dpId) {
        return dataPointAggregateMap.containsKey(dpId) && !subDataPointIdSet.contains(dpId);
    }
}
