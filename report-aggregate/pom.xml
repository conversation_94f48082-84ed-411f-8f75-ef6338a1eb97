<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>mart-component-gateway</artifactId>
        <groupId>com.morningstar</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>report-aggregate</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Core -->
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>data-point-loader</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>data-point-loader-test</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>entitlement-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>gateway-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>uim-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>redisson-lock</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>async-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>rdb-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>calculation-lib-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- Core: mart-component-cloud -->
        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>ecs-metadata-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>ecs-metadata-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Data -->
        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>mart-equity-package</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>fixed-income-ice-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>ph-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>custom-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>lei-nace-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>rds-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>eod-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- Services -->
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>ph-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>mart-gateway</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>time-series-data</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                        <configuration>
                            <dataFileIncludes>
                                <dataFileInclude>**/jacoco.exec</dataFileInclude>
                            </dataFileIncludes>
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-aggregate</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>