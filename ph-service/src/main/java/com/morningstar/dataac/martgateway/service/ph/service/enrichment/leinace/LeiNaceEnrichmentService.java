package com.morningstar.dataac.martgateway.service.ph.service.enrichment.leinace;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.IdLevelType;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.MandatoryDataPointId;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PerformanceCategory;
import com.morningstar.dataac.martgateway.data.ph.entity.request.StreamHoldingRequest;
import com.morningstar.dataac.martgateway.data.ph.service.enrichment.HoldingDetailsEnrichmentService;
import com.morningstar.dataac.martgateway.data.ph.util.PerformanceUtil;
import com.morningstar.dataac.morningstar.data.leinace.service.gateway.LeiNaceGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class LeiNaceEnrichmentService implements HoldingDetailsEnrichmentService {
    private final LeiNaceGateway leiNaceGateway;

    public LeiNaceEnrichmentService(LeiNaceGateway leiNaceGateway) {
        this.leiNaceGateway = leiNaceGateway;
    }

    @Override
    public Mono<List<Map.Entry<Integer, Map<String, Object>>>> getEnrichmentData(List<Map<String, Object>> holdingDetails, StreamHoldingRequest request, PerformanceUtil performanceUtil) {
        if (leiNaceGateway == null) {
            log.warn("LEI NACE Gateway is not enabled");
            return Mono.just(List.of());
        }

        Map<String, Integer> investmentIdToIndex = new HashMap<>();
        for (int i = 0; i < holdingDetails.size(); i++) {
            Object invId = holdingDetails.get(i).get(MandatoryDataPointId.SEC_ID_DP_ID.getId());
            if (invId != null) {
                investmentIdToIndex.put(invId.toString(), i);
            }
        }

        performanceUtil.addMartRequests(request.getMappingReqDpToIdLevelType().keySet().size());
        return Flux.fromIterable(request.getMappingReqDpToIdLevelType().entrySet()).flatMap(entry -> buildEnrichmentData(entry, holdingDetails, investmentIdToIndex, request))
                .collectList();
    }


    private Flux<Map.Entry<Integer, Map<String, Object>>> buildEnrichmentData(Map.Entry<IdLevelType, List<DataPoint>> entry, List<Map<String, Object>> holdingDetails, Map<String, Integer> investmentIdToIndex, StreamHoldingRequest request) {
        Map<String, List<String>> mappingSrcInvIdMap = new HashMap<>();
        holdingDetails.forEach(holding -> {
            Object mappingObj = holding.get(entry.getKey().getPhDataPointId());
            if (mappingObj instanceof String mappingKey && StringUtils.isNotEmpty(mappingKey)) {
                Object secId = holding.get(MandatoryDataPointId.SEC_ID_DP_ID.getId());
                if (secId != null) {
                    mappingSrcInvIdMap.computeIfAbsent(mappingKey, k -> new ArrayList<>())
                            .add(secId.toString());
                } else {
                    log.warn("Missing SEC_ID_DP_ID for holding: {}", holding);
                }
            }
        });

        List<String> mappingValueList = new ArrayList<>(mappingSrcInvIdMap.keySet());
        if (CollectionUtils.isEmpty(mappingValueList)) {
            return Flux.empty();
        }

        MartRequest martRequest = MartRequest.builder().requestId(request.getHoldingDataRequest().getRequestId()).ids(mappingValueList).
                dps(new ArrayList<>(entry.getValue().stream().map(DataPoint::getNid).collect(Collectors.toSet()))).
                fromPortfolioHoldings(true).readCache(request.getHoldingDataRequest().isReadCache() ? "true" : "false").build();

        return fetchLeiNaceResults(martRequest).flatMap(result -> {
            String mappingId = result.getId();
            return Flux.fromIterable(mappingSrcInvIdMap.getOrDefault(mappingId, Collections.emptyList()))
                    .map(invId -> {
                        Integer index = investmentIdToIndex.get(invId);
                        if (index != null) {
                            Map<String, Object> newFields = new HashMap<>();
                            if (result instanceof CurrentResult currentResult) {
                                newFields.putAll(currentResult.getValues());
                            }
                            if (!newFields.isEmpty()) {
                                return Optional.of(new AbstractMap.SimpleEntry<>(index, newFields));
                            }
                        }
                        return Optional.empty();
                    })
                    .filter(Optional::isPresent)
                    .map(e -> (Map.Entry<Integer, Map<String, Object>>) e.get());
        });
    }

    private Flux<Result> fetchLeiNaceResults(MartRequest martRequest) {
        return leiNaceGateway.retrieve(martRequest);

    }

    @Override
    public boolean isApplicable(List<Map<String, Object>> holdingDetails, StreamHoldingRequest streamRequest) {
        return !MapUtils.isEmpty(streamRequest.getMappingReqDpToIdLevelType());
    }

    @Override
    public PerformanceCategory getPerformanceCategory() {
        return PerformanceCategory.LEI_NACE_ENRICHMENT;
    }
}
