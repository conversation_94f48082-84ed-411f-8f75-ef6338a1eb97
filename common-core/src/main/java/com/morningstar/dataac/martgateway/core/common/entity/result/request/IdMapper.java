package com.morningstar.dataac.martgateway.core.common.entity.result.request;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;

public abstract class IdMapper {

    public abstract String getId(String type);

    public abstract String getInvestmentId();

    public abstract R transformToResponse();

    public abstract boolean isPrivateModel();

    public abstract String getSecurityType();

    public abstract String getPostTaxForMixSetting();

    public abstract String getStatus();

    public abstract boolean isPrivateId();

    public abstract boolean isIndex();
}
