package com.morningstar.dataac.martgateway.core.common.entity.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.T;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import reactor.util.Logger;
import reactor.util.Loggers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TsResult extends Result {

    private Map<String, TsDataProtoBuf.TSDataDouble> values;
    private static final Logger LOGGER = Loggers.getLogger(TsResult.class);
    private static final List<T> EMPTY_LIST = List.of();

    public TsResult(String id, Map<String, TsDataProtoBuf.TSDataDouble> values) {
        this.id = id;
        this.values = values;
    }

    /**
     * @return data points of retrieve result
     */
    @Override
    public Map<String, TsDataProtoBuf.TSDataDouble> getValues() {
        return values;
    }

    @Override
    public Map getPrimaryKeys() {
        return Map.of();
    }

    /**
     * @return
     */
    public List<T> transform() {
        if (MapUtils.isEmpty(this.getValues())) {
            return Arrays.asList(new T("404", new ArrayList<>()));
        }
        return EMPTY_LIST;
    }

    @Override
    public void updateValues(String dataPointId, Function<String, String> function) {
        try {
            if (MapUtils.isEmpty(values)) {
                return;
            }
            TsDataProtoBuf.TSDataDouble tsData = values.get(dataPointId);
            if (tsData == null) {
                return;
            }

            TsDataProtoBuf.TSDataDouble.Builder builder = tsData.toBuilder();
            List<Double> originalValues = tsData.getValuesList();

            builder.clearValues();

            for (Double originalValue : originalValues) {
                String originalValueStr = String.valueOf(originalValue);
                String transformedValueStr = function.apply(originalValueStr);

                Double transformedValue = parseTransformedValue(transformedValueStr, originalValue, dataPointId);
                builder.addValues(transformedValue);
            }

            values.put(dataPointId, builder.build());

        } catch (Exception e) {
            LOGGER.error("Exception in TsResult.updateValues: Error transforming for dpId: {}, id: {}. Error: {}",
                        dataPointId, id, e.getMessage());
        }
    }

    private Double parseTransformedValue(String transformedValueStr, Double originalValue, String dataPointId) {
        try {
            return Double.valueOf(transformedValueStr);
        } catch (NumberFormatException e) {
            LOGGER.warn("Failed to parse transformed value '{}' as Double for dpId: {}, id: {}. Using original value.",
                       transformedValueStr, dataPointId, id);
            return originalValue;
        }
    }

    @Override
    public void renameDataPoint(String originalDataPointId, String targetDataPointId) {
        if (MapUtils.isNotEmpty(values) && values.containsKey(originalDataPointId)) {
            TsDataProtoBuf.TSDataDouble value = values.get(originalDataPointId);
            values.remove(originalDataPointId);
            values.put(targetDataPointId, value);
        }
    }

    @Override
    public void duplicateContent(String originalDataPointId, String targetDataPointId) {
        if (MapUtils.isNotEmpty(values) && values.containsKey(originalDataPointId)) {
            values.put(targetDataPointId, values.get(originalDataPointId));
        }
    }
}
