package com.morningstar.dataac.martgateway.core.common.entity.result.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MartRequest implements UseCaseRequest {
    private String useCase;
    private List<String> ids;
    private List<IdDatePair> idPairs;
    private String idType;
    private List<String> dps;
    private List<GridviewDataPoint> customCalcDataPoints;
    private String startDate;
    private String endDate;
    private String currency;
    private String preCurrency;
    private String adjustment;
    private String readCache;
    private String productId;
    private String categoryCode;
    private String peerGroupId;
    private String regionId;
    private String universe;
    private String requestId;
    private String serviceName;
    private String tsId;

    @JsonIgnore
    private List<IdMapper> idMappers;

    private String userId;
    private String dueStage;
    private String dateFormat;
    private String decimalFormat;
    private String extendedPerformance;
    private String postTax;
    private Boolean useRequireId;
    private String frequency;
    private String requireContinueData;
    private String shareType;
    private String shareClassStatus;
    private String investmentType;
    private Integer top;
    private String language;
    //equity data:
    private Map<String, Set<String>> aliasMap;
    private EquityMetaData equityMetaData;
    private boolean checkEntitlement = true;
    private boolean useNewCCS = false;
    private String entitlementProductId;
    private String configId;
    private boolean fromPortfolioHoldings;
    private List<String> euTaxonomyObjective;
    private List<String> euTaxonomyActivityClusterType;
    private List<String> euTaxonomyNonNGObjective;
    private List<String> distributionType;
    private List<String> specialDistribution;
    private Map<String, List<String>> allParameters;
    private List<String> datapointIds;

    private Map<String, Object> attributes = new HashMap<>();

    public void distinct() {
        List<String> distinctDps = dps.stream().distinct().collect(Collectors.toList());
        List<String> distinctIds = ids.stream().distinct().collect(Collectors.toList());
        this.setDps(distinctDps);
        this.setIds(distinctIds);
    }

    public boolean isIdPair(){
        return idPairs != null && !idPairs.isEmpty();
    }

    public boolean isPostTax() {
        return "1".equals(postTax);
    }

    public void removeNullLists(){
        ids = (ids == null) ? new ArrayList<>() : ids;
        dps = (dps == null) ? new ArrayList<>() : dps;
    }

    public String getRequestParam(String path, String query) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(path);

        if (StringUtils.isNotBlank(query)) {
            stringBuilder.append("?");
            stringBuilder.append(query);
        }
        return stringBuilder.toString();
    }

    public List<String> getListOfIds() {
        return isIdPair() ? idPairs.stream().map(i -> i.getId()).collect(Collectors.toList()) : ids;
    }

    public boolean isTimeSeriesRequest() {
        return StringUtils.isNotEmpty(getStartDate()) && StringUtils.isNotEmpty(getEndDate());
    }

    public void setAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new HashMap<>();
        }
        this.attributes.put(key, value);
    }

    public Object getAttribute(String key) {
        if (this.attributes == null) {
            return null;
        }
        return this.attributes.get(key);
    }

    public boolean isReadCache() {
        return StringUtils.isEmpty(this.readCache) ||
                !"false".equalsIgnoreCase(this.readCache);
    }

    public boolean isCustomCalcDataPoint(String id) {
        return CollectionUtils.isNotEmpty(customCalcDataPoints) && customCalcDataPoints.stream().anyMatch(dp -> dp.getDataPointId().equals(id));
    }

    public MartRequest shallowCopy(){
        return MartRequest.builder()
                .useCase(this.getUseCase())
                .ids(this.getIds())
                .idPairs(this.getIdPairs())
                .idType(this.getIdType())
                .dps(this.getDps())
                .startDate(this.getStartDate())
                .endDate(this.getEndDate())
                .currency(this.getCurrency())
                .preCurrency(this.getPreCurrency())
                .adjustment(this.getAdjustment())
                .readCache(this.getReadCache())
                .productId(this.getProductId())
                .entitlementProductId(this.getEntitlementProductId())
                .categoryCode(this.getCategoryCode())
                .peerGroupId(this.getPeerGroupId())
                .regionId(this.getRegionId())
                .universe(this.getUniverse())
                .requestId(this.getRequestId())
                .idMappers(this.getIdMappers())
                .userId(this.getUserId())
                .dueStage(this.getDueStage())
                .dateFormat(this.getDateFormat())
                .decimalFormat(this.getDecimalFormat())
                .extendedPerformance(this.getExtendedPerformance())
                .postTax(this.getPostTax())
                .useRequireId(this.getUseRequireId())
                .frequency(this.getFrequency())
                .requireContinueData(this.getRequireContinueData())
                .shareType(this.getShareType())
                .shareClassStatus(this.getShareClassStatus())
                .investmentType(this.getInvestmentType())
                .top(this.getTop())
                .language(this.getLanguage())
                .aliasMap(this.getAliasMap())
                .equityMetaData(this.getEquityMetaData())
                .checkEntitlement(this.isCheckEntitlement())
                .useNewCCS(this.isUseNewCCS())
                .attributes(this.getAttributes())
                .configId(this.getConfigId())
                .fromPortfolioHoldings(this.isFromPortfolioHoldings())
                .euTaxonomyObjective(this.getEuTaxonomyObjective())
                .euTaxonomyObjective(this.getEuTaxonomyActivityClusterType())
                .euTaxonomyObjective(this.getEuTaxonomyNonNGObjective())
                .distributionType(this.getDistributionType())
                .specialDistribution(this.getSpecialDistribution())
                .datapointIds((this.getDatapointIds()))
                .build();
    }
}
