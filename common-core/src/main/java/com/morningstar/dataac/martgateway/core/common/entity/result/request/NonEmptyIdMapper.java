package com.morningstar.dataac.martgateway.core.common.entity.result.request;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONObject;

public class NonEmptyIdMapper extends IdMapper {

    private String investmentId;
    private JSONObject jsonObject;

    public NonEmptyIdMapper(String investmentId, String value) {
        this.investmentId = investmentId;
        this.jsonObject = new JSONObject(value);
    }

    public NonEmptyIdMapper(String investmentId, JSONObject jsonObject) {
        this.investmentId = investmentId;
        this.jsonObject = jsonObject;
    }

    @Override
    public String getId(String type) {
        return jsonObject.optString(type);
    }

    @Override
    public String getInvestmentId() {
        return this.investmentId;
    }

    @Override
    public R transformToResponse() {
        R response = new R(investmentId);
        List<V> valueList = new ArrayList<>();
        for (String key: jsonObject.keySet()) {
            valueList.add(new V(key, jsonObject.optString(key)));
        }
        response.setC(valueList);
        return response;
    }

    @Override
    public String getSecurityType() { return jsonObject.optString("SecurityType"); }

    @Override
    public boolean isPrivateModel() {
        return jsonObject.has("IsPrivateModel") && Boolean.parseBoolean(jsonObject.optString("IsPrivateModel"));
    }

    @Override
    public String getPostTaxForMixSetting() {
        return jsonObject.optString("PostTaxForMixSetting");
    }

    @Override
    public String getStatus() {
        return jsonObject.optString("Status");
    }

    @Override
    public boolean isPrivateId() {
        return jsonObject.optString("Status").equals("254");
    }

    @Override
    public boolean isIndex() {
        return jsonObject.optString("SecurityType").equals("XI");
    }
}
