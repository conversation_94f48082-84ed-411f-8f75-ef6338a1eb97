package com.morningstar.dataac.martgateway.core.common.entity.investmentapi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.EquityMetaData;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.UseCaseRequest;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvestmentApiRequest implements UseCaseRequest {
    public static final Pattern IS_ID_WITH_UNIVERSE_ONLY = Pattern.compile("^.{10};.{2}$");

    private String useCase = "feed";
    private Instant deltaStartTime;
    private String language;
    private String idType;
    @JsonProperty("datapoints")
    List<GridviewDataPoint> dataPoints;
    List<Investment> investments;
    private String readCache;
    private String productId;
    private String serviceName;
    private String tsId;

    private String entitlementProductId;
    private String requestId;
    private String userId;
    private String configId;
    private boolean checkEntitlement = true;
    private boolean includeUnchangedData;

    private int directDpsBatchSize = 5;
    private int directIdBatchSize = 25;

    @JsonIgnore
    private List<IdMapper> idMappers;
    private boolean skipProxy;
    @JsonIgnore
    private boolean isDeltaDetection = false;

    public boolean validateRequest() {
        return areAliasesUnique() && isUseCaseValid() && StringUtils.isNotEmpty(userId);
    }

    private boolean areAliasesUnique() {
        List<String> aliases = dataPoints.stream().map(GridviewDataPoint::getAlias).filter(Objects::nonNull).collect(Collectors.toList());
        aliases.addAll(dataPoints.stream().filter(dp -> MapUtils.isNotEmpty(dp.getAliases())).flatMap(dp -> dp.getAliases().values().stream()).toList());
        Set<String> set = new HashSet<>(aliases);
        return set.size() == aliases.size();
    }

    private boolean isUseCaseValid() {
        return "view".equalsIgnoreCase(this.getUseCase()) || "export".equalsIgnoreCase(this.getUseCase()) || "feed".equalsIgnoreCase(this.getUseCase());
    }

    public boolean isDeltaRequest() {
        return this.getDeltaStartTime() != null;
    }

    public List<MartRequest> transformToMartRequests(Boolean useNewCCS) {
        List<MartRequest> martRequests = new ArrayList<>();
        Map<DataPointRequestConfiguration, List<GridviewDataPoint>> dataPointsByConfiguration = groupDataPointsByConfiguration(this);
        for (Map.Entry<DataPointRequestConfiguration, List<GridviewDataPoint>> entry : dataPointsByConfiguration.entrySet()) {
            DataPointRequestConfiguration dataPointConfiguration = entry.getKey();
            List<GridviewDataPoint> dataPoints = entry.getValue();
            MartRequest martRequest = buildMartRequestWithoutDpConfig(useNewCCS);
            injectDataPointConfiguration(martRequest, dataPointConfiguration);
            injectDataPointParameter(martRequest, dataPoints);

            martRequest.setDps(dataPoints.stream().map(GridviewDataPoint::getDataPointId).distinct().collect(Collectors.toList()));
            martRequest.setAliasMap(getAliasMap(dataPoints));
            martRequests.add(martRequest);
        }
        return martRequests;
    }

    public static Map<String, Set<String>> getAliasMap(List<GridviewDataPoint> dataPointsList){
        return dataPointsList.stream()
                .filter(d -> d.getAlias() != null)
                .collect(Collectors.groupingBy(GridviewDataPoint::getDataPointId, Collectors.mapping(GridviewDataPoint::getAlias, Collectors.toSet())));
    }

    @JsonIgnore
    public List<String> getAllInvestmentIds() {
        return (CollectionUtils.isNotEmpty(investments)) ? investments.stream().map(Investment::getId).collect(Collectors.toList()) : Collections.emptyList();
    }

    private Map<DataPointRequestConfiguration, List<GridviewDataPoint>> groupDataPointsByConfiguration(InvestmentApiRequest request) {
        Map<DataPointRequestConfiguration, List<GridviewDataPoint>> dataPointsByConfiguration = new HashMap<>();
        for (GridviewDataPoint dataPoint : request.getDataPoints()) {
            DataPointRequestConfiguration configuration = DataPointRequestConfiguration.extractConfiguration(dataPoint);
            List<GridviewDataPoint> sameConfigDataPoints = dataPointsByConfiguration.computeIfAbsent(configuration, k -> new ArrayList<>());
            sameConfigDataPoints.add(dataPoint);
        }
        return dataPointsByConfiguration;
    }

    public void reformatIdWithUniverseOnly() {
        this.getInvestments().forEach(investment -> {
            // if the id is not a hybrid id with ';' and not ']', then it is an id with universe only.
            if (isIdWithUniverseOnly(investment.getId())) {
                investment.setId(investment.getId().split(";")[0]);
            }
        });
    }

    public boolean canReadCache() {
        return this.readCache != null && this.readCache.equalsIgnoreCase("Yes");
    }

    public MartRequest buildMartRequestWithoutDpConfig(Boolean useNewCCS) {
        return MartRequest.builder()
                .ids(getAllInvestmentIds())
                .idType(this.getIdType())
                .requestId(this.getRequestId())
                .readCache(this.getReadCache())
                .productId(this.getProductId())
                .entitlementProductId(this.getEntitlementProductId())
                .userId(this.getUserId())
                .configId(this.getConfigId())
                .useCase(this.getUseCase())
                .idMappers(this.getIdMappers())
                .language(this.getLanguage())
                .useNewCCS(useNewCCS)
                .build();
    }

    private void injectDataPointConfiguration(MartRequest request, DataPointRequestConfiguration config) {
        request.setFrequency(config.getFrequency());
        request.setStartDate(config.getStartDate());
        request.setEndDate(config.getEndDate());
        request.setPreCurrency(config.getPreCurrency());
        request.setCurrency(config.getCurrency());
        request.setRequireContinueData(config.getRequireContinueData());
        request.setPostTax(config.getPostTax());
        request.setDateFormat(config.getDateFormat());
        request.setDecimalFormat(config.getDecimalFormat());
        request.setExtendedPerformance(config.getExtendedPerformance());

        if (hasEquityData(config)) {
            EquityMetaData data = new EquityMetaData();
            data.setPeriod(config.getPeriod());
            data.setReportType(config.getReportType());
            data.setCountryId(config.getCountryId());
            data.setShareType(config.getShareType());
            data.setLanguageCode(config.getLanguageCode());
            data.setNumberOfMonths(config.getNumberOfMonths());
            data.setStartDate(config.getStartDate());
            data.setEndDate(config.getEndDate());
            data.setSegmentType(config.getSegmentType());
            data.setCorporateActionType(config.getCorporateActionType());
            data.setQuantitativeSmartTextType(config.getQuantitativeSmartTextType());
            data.setAddressType(config.getAddressType());
            data.setOwnerTypeGroup(config.getOwnerTypeGroup());
            data.setEventId(config.getEventId());
            data.setEventStatus(config.getEventStatus());
            data.setTransactionType(config.getTransactionType());
            data.setLatestData(config.isLatestData());
            request.setEquityMetaData(data);
        }
    }

    private boolean hasEquityData(DataPointRequestConfiguration config) {
        return CollectionUtils.isNotEmpty(config.getPeriod()) || CollectionUtils.isNotEmpty(config.getReportType())
                || CollectionUtils.isNotEmpty(config.getCountryId()) || CollectionUtils.isNotEmpty(config.getLanguageCode())
                || CollectionUtils.isNotEmpty(config.getShareType()) || CollectionUtils.isNotEmpty(config.getNumberOfMonths())
                || CollectionUtils.isNotEmpty(config.getSegmentType()) || StringUtils.isNotEmpty(config.getStartDate())
                || StringUtils.isNotEmpty(config.getEndDate())
                || CollectionUtils.isNotEmpty(config.getCorporateActionType())
                || CollectionUtils.isNotEmpty(config.getQuantitativeSmartTextType())
                || CollectionUtils.isNotEmpty(config.getAddressType())
                || CollectionUtils.isNotEmpty(config.getOwnerTypeGroup())
                || CollectionUtils.isNotEmpty(config.getEventId())
                || CollectionUtils.isNotEmpty(config.getEventStatus())
                || CollectionUtils.isNotEmpty(config.getTransactionType());
    }

    @JsonIgnore
    public List<GridviewDataPoint> getAllDataPoints() {
        List<GridviewDataPoint> list = new LinkedList<>(this.getDataPoints());
        List<GridviewDataPoint> specialArrayIdList = list.stream().filter(dp -> CollectionUtils.isNotEmpty(dp.getDataPointIds())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(specialArrayIdList)) {
            specialArrayIdList.forEach(dp -> {
                List<String> dps = dp.getDataPointIds();

                if (CollectionUtils.isNotEmpty(dps)) {
                    Map<String, String> aliases = dp.getAliases();
                    GridviewDataPoint source = dp.toBuilder().dataPointIds(null).alias(null).build();
                    dps.forEach(id -> {
                        GridviewDataPoint newDp = source.toBuilder().dataPointId(id).build();
                        if (MapUtils.isNotEmpty(aliases)) {
                            String alias = aliases.get(id);
                            newDp.setAlias(alias);
                        }
                        list.add(newDp);
                    });
                }
            });

            list.removeAll(specialArrayIdList);
        }

        return list;
    }

    @JsonIgnore
    public Set<String> getAllDataPointsSet(){
        return this.getAllDataPoints().stream()
                .map(GridviewDataPoint::getDataPointId)
                .collect(Collectors.toSet());
    }

    public InvestmentApiRequest shallowCopy(){
        InvestmentApiRequest investmentApiRequest = this.shallowCopyWithoutDataPointsInvestments();
        //note: clone and original will reference the same individual objects, but different list memory
        if(this.dataPoints != null) {
            List<GridviewDataPoint> gridviewDataPoints = new ArrayList<>();
            gridviewDataPoints.addAll(this.dataPoints);
            investmentApiRequest.setDataPoints(gridviewDataPoints);
        }
        if(this.investments != null) {
            List<Investment> investmentsList = new ArrayList<>();
            investmentsList.addAll(this.investments);
            investmentApiRequest.setInvestments(investmentsList);
        }
        return investmentApiRequest;
    }

    public InvestmentApiRequest shallowCopyWithoutDataPointsInvestments(){
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder().useCase(this.useCase)
                .language(this.language).idType(this.idType).readCache(this.readCache).productId(this.productId)
                .requestId(this.requestId).userId(this.userId).checkEntitlement(this.checkEntitlement)
                .entitlementProductId(this.entitlementProductId).configId(this.configId)
                .isDeltaDetection(this.isDeltaDetection).includeUnchangedData(this.includeUnchangedData).build();

        if(this.idMappers != null) {
            List<IdMapper> idMapperList = new ArrayList<>();
            idMapperList.addAll(this.idMappers);
            investmentApiRequest.setIdMappers(idMapperList);
        }
        return investmentApiRequest;
    }

    public boolean isIdWithUniverseOnly(String input) {
        Matcher matcher = IS_ID_WITH_UNIVERSE_ONLY.matcher(input);
        return matcher.matches();
    }

    private void injectDataPointParameter(MartRequest request, List<GridviewDataPoint> dataPoints) {
        if (CollectionUtils.isEmpty(dataPoints)) {
            return;
        }

        List<String> dataPointIds = dataPoints.stream()
                .map(GridviewDataPoint::getDataPointId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        request.setDatapointIds(dataPointIds);

        List<String> euTaxonomyObjectives = dataPoints.stream()
                .map(GridviewDataPoint::getEuTaxonomyObjective)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        request.setEuTaxonomyObjective(euTaxonomyObjectives);
        addParametersToCollection("euTaxonomyObjectives", euTaxonomyObjectives, request);

        List<String> eUTaxonomyActivityClusterType = dataPoints.stream()
                .map(GridviewDataPoint::getEuTaxonomyActivityClusterType)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        request.setEuTaxonomyActivityClusterType(eUTaxonomyActivityClusterType);
        addParametersToCollection("eUTaxonomyActivityClusterType", eUTaxonomyActivityClusterType, request);

        List<String> euTaxonomyNonNGObjective = dataPoints.stream()
                .map(GridviewDataPoint::getEuTaxonomyNonNGObjective)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        request.setEuTaxonomyNonNGObjective(euTaxonomyNonNGObjective);
        addParametersToCollection("euTaxonomyNonNGObjective", euTaxonomyNonNGObjective, request);

        List<String> distributionType = dataPoints.stream()
                .map(GridviewDataPoint::getDistributionType)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        request.setDistributionType(distributionType);
        addParametersToCollection("distributionType", distributionType, request);

        List<String> specialDistribution = dataPoints.stream()
                .map(GridviewDataPoint::getSpecialDistribution)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        request.setSpecialDistribution(specialDistribution);
        addParametersToCollection("specialDistribution", specialDistribution, request);
    }

    private void addParametersToCollection(String parameterName, List<String> parameterValues, MartRequest request) {
        if (CollectionUtils.isEmpty(parameterValues))
            return;

        if (request.getAllParameters() == null)
            request.setAllParameters(new HashMap<>());
        request.getAllParameters().put(parameterName, parameterValues);
    }
}
