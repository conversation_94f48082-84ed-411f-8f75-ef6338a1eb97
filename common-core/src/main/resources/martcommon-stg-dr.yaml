martcommon:
  aws:
    region: us-west-2
    s3:
      region: eu-west-1
      mode: fixed
      default-env: blue
      bucket: mart-data-stg-eu
      productIdsRegistration: product_ids_registration.txt
      serviceNameRegistration: service_name_registration.txt
    ecs:
      metadata:
        baseUrl: ${ECS_CONTAINER_METADATA_URI_V4:}
  redis:
    storage:
      host: mart-data-v7-cluster-stg-eu-west-1.date7ebe.easn.morningstar.com
      port: 6379
    cache:
      host: application-cache-v7-cluster-stg-eu-west-1.dpd8eb6b.easn.morningstar.com
      port: 6379
    time-series-cache:
      host: application-cache-v7-cluster-stg-eu-west-1.dpd8eb6b.easn.morningstar.com
      port: 6379
      server-type: Cluster
    meta-data-cache:
      host: mart-meta-data-v8-cluster-stg-eu-west-1.dpd8eb6b.easn.morningstar.com
      port: 6379
    ts-data:
      host: ts-data-v8-cluster-stg.bg4uxb.clustercfg.use1.cache.amazonaws.com
      port: 6379
  localCache:
    count: 2000000
    enableCache: false
    queueSize: 10
    s3File:
      idMapper: activeSecIds
      deltaId: deltaSecIds