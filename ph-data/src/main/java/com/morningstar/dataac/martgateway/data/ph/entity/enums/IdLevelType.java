package com.morningstar.dataac.martgateway.data.ph.entity.enums;

import lombok.Getter;

@Getter
public enum IdLevelType {

    ISIN("ISIN","HS761"),
    COMPANY_ID("CompanyId", "HS778");

    private final String idLevel;
    private final String phDataPointId;

    IdLevelType(String idLevel, String phDataPointId) {
        this.idLevel = idLevel;
        this.phDataPointId = phDataPointId;
    }

    public static IdLevelType getByIdLevel(String idLevel) {
        for (IdLevelType type : IdLevelType.values()) {
            if (type.getIdLevel().equals(idLevel)) {
                return type;
            }
        }
        return null;
    }
}
