package com.morningstar.martequity;

import com.morningstar.dataac.martgateway.core.calculationlib.CalculationLibConversionService;
import com.morningstar.dataac.martgateway.core.calculationlib.entity.CalcMetaData;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.EquityDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.MongodbCollection;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EquityCalculationLibServiceTest {

    private EquityCalculationLibService equityCalculationLibService;

    @Mock
    private CalculationLibConversionService equityCalculationLibConversionService;

    private MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic;

    @BeforeEach
    void setUp() {
        dataPointRepositoryMockedStatic = mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
        equityCalculationLibConversionService = mock(CalculationLibConversionService.class);
        equityCalculationLibService = new EquityCalculationLibService(equityCalculationLibConversionService);
    }

    @AfterEach
    void afterEachTest() {
        dataPointRepositoryMockedStatic.close();
    }

    @Test
    public void convertCurrentResultTest() {


        DataPoint eqf99 = DataPoint.builder()
                .id("EQF99")
                .nid("EQF99")
                .src("EQUITY")
                .build();

        DataPoint eqf49 = DataPoint.builder()
                .id("EQF49")
                .nid("EQF49")
                .src("EQUITY")
                .build();


        Calculation calculation = Calculation.builder()
                .cur(eqf99)
                .endDate(eqf49)
                .calSrc("CALCLIB")
                .build();

        EquityDataPoint equityDataPoint = EquityDataPoint.builder()
                .build();

        DataPoint eqcyx = DataPoint.builder()
                .id("EQCYX")
                .nid("EQCYX")
                .src("Equity")
                .idLevel("performanceId")
                .calculation(calculation)
                .equityDatapoint(equityDataPoint)
                .build();

        CalcRequest calcLibRequest = CalcRequest.builder()
                .idMappers(List.of(new TestIdMapper()))
                .idList(List.of("0P000000GY"))
                .calcDps(List.of("EQCYX"))
                .currency("CAD")
                .annualized("true")
                .userId("2F113DDE-CE80-4E2F-A767-271E590141BA")
                .build();

        HashMap valuesMap = new HashMap();
        valuesMap.put("EQCYX", "100.00");
        valuesMap.put("EQF99", "USD");
        valuesMap.put("EQF49", "2025-05-10");

        HashMap outcomeMap = new HashMap();
        valuesMap.put("EQCYX", "200.00");
        valuesMap.put("EQF99", "USD");
        valuesMap.put("EQF49", "2025-05-10");


        Flux<Result> result = Flux.just(
                new CurrentResult("0P000000GY", valuesMap, Instant.now())
        );

        when(equityCalculationLibConversionService.convertCurrent(any(CurrentResult.class), any(Set.class), anyString(), any()))
                .thenReturn(new CurrentResult("0P000000GY", valuesMap, Instant.now()));

        when(DataPointRepository.getByNid(anyString())).thenAnswer(invocation -> {
            String nid = invocation.getArgument(0);
            if ("EQCYX".equals(nid)) {
                return eqcyx;
            } else if ("EQF99".equals(nid)) {
                return DataPoint.builder().nid("EQF99").name("Currency Code").src("EQUITY").build();
            } else if ("EQF49".equals(nid)) {
                return DataPoint.builder().nid("EQF49").name("End Date").src("EQUITY").build();
            }
            return null;
        });

        Flux<Result> outcome = equityCalculationLibService.retrieveCalcLib(calcLibRequest, result);

        StepVerifier.create(outcome)
                .expectSubscription()
                .assertNext(res -> {
                    assertEquals("0P000000GY", res.getId());
                    assertEquals("200.00", res.getValues().get("EQCYX"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void convertTimeSeriesResultTest() {
        DataPoint eqf99 = DataPoint.builder().id("EQF99").nid("EQF99").src("EQUITY").build();
        DataPoint eqf49 = DataPoint.builder().id("EQF49").nid("EQF49").src("EQUITY").build();
        Calculation calculation = Calculation.builder().cur(eqf99).endDate(eqf49).calSrc("CALCLIB").build();
        EquityDataPoint equityDataPoint = EquityDataPoint.builder().timeSeriesCollection(MongodbCollection.builder().build()).timeSeriesField("yes").build();

        DataPoint eqcyx = DataPoint.builder()
                .id("EQCYX")
                .nid("EQCYX")
                .src("Equity")
                .idLevel("performanceId")
                .calculation(calculation)
                .equityDatapoint(equityDataPoint)
                .build();

        CalcRequest calcRequest = CalcRequest.builder()
                .idList(List.of("0C000006SS"))
                .calcDps(List.of("EQCYX"))
                .currency("GBP")
                .startDate("2002-01-01")
                .endDate("2003-01-01")
                .annualized("true")
                .readCache("true")
                .userId("2F113DDE-CE80-4E2F-A767-271E590141BA")
                .build();

        Map<String, List<V>> valuesMap = new HashMap<>();
        valuesMap.put("EQCYX", new ArrayList<>(List.of(
                new V("2025-05-15", "0.033497"),
                new V("2025-05-14", "0.033360"),
                new V("2025-05-13", "0.033267"),
                new V("2025-05-12", "0.033600")
        )));
        valuesMap.put("EQF99", new ArrayList<>(List.of(
                new V("2025-05-15", "USD"),
                new V("2025-05-14", "USD"),
                new V("2025-05-13", "USD"),
                new V("2025-05-12", "USD")
        )));
        valuesMap.put("EQF49", new ArrayList<>(List.of(
                new V("2025-05-15", "2025-05-15"),
                new V("2025-05-14", "2025-05-14"),
                new V("2025-05-13", "2025-05-13"),
                new V("2025-05-12", "2025-05-12")
        )));

        when(equityCalculationLibConversionService.convertTS(
                any(TimeSeriesResult.class), any(Set.class), anyString(), any(), any(CalcMetaData.class)))
                .thenReturn(new TimeSeriesResult("0P000000GY", valuesMap));

        when(DataPointRepository.getByNid(anyString())).thenAnswer(invocation -> {
            String nid = invocation.getArgument(0);
            if ("EQCYX".equals(nid)) {
                return eqcyx;
            } else if ("EQF99".equals(nid)) {
                return DataPoint.builder().nid("EQF99").name("Currency Code").src("EQUITY").build();
            } else if ("EQF49".equals(nid)) {
                return DataPoint.builder().nid("EQF49").name("End Date").src("EQUITY").build();
            }
            return null;
        });

        Flux<Result> result = Flux.just(new TimeSeriesResult("0P000000GY", valuesMap));
        Flux<Result> outcome = equityCalculationLibService.retrieveCalcLib(calcRequest, result);

        StepVerifier.create(outcome)
                .expectSubscription()
                .assertNext(res -> {
                    assertEquals("0P000000GY", res.getId());
                    TimeSeriesResult tsResult = (TimeSeriesResult) res;
                    assertEquals(4, tsResult.getValues().get("EQCYX").size());
                    assertEquals("0.033497", tsResult.getValues().get("EQCYX").get(0).getV());
                    assertEquals("0.033360", tsResult.getValues().get("EQCYX").get(1).getV());
                    assertEquals("0.033267", tsResult.getValues().get("EQCYX").get(2).getV());
                    assertEquals("0.033600", tsResult.getValues().get("EQCYX").get(3).getV());
                })
                .expectComplete()
                .verify();
    }
}


class TestIdMapper extends IdMapper {
    @Override
    public String getId(String type) { return "t1"; }
    @Override
    public String getInvestmentId() { return "t2"; }
    @Override
    public R transformToResponse() { return null; }
    @Override
    public boolean isPrivateModel() { return false; }
    @Override
    public String getSecurityType() { return "t3"; }
    @Override
    public String getPostTaxForMixSetting() { return "t4"; }
    @Override
    public String getStatus() { return "t5"; }
    @Override
    public boolean isPrivateId() { return false; }

    @Override
    public boolean isIndex() {
        return false;
    }
}