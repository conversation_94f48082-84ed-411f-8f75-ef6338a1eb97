package com.morningstar.martgateway.util;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class CCSEntitlementUtil {

    public static final String CCS_SERVICE_NAME = "custom-calculation-service";
    public static final String CCS_TS_ID = "TS00553";

    public static final String DP_BASE_CURRENCY = "OS05M";
    public static final String DP_INCEPTION_DATE = "DT042";

    public static final String TIME_SERIES_ENDPOINT = "1";

    public static final String GRID_VIEW_ENDPOINT = "2";

    public static final String CURRENCY_TYPE = "CU";

    private CCSEntitlementUtil() {
    }

    public static boolean isCheckEntitlement(String serviceName, String tsId, List<String> dps, List<IdMapper> idMappers, String endPoint) {
        return !canBypassForCCS(serviceName, tsId, dps, idMappers, endPoint);
    }

    public static boolean canBypassForCCS(String serviceName, String tsId, List<String> dps, List<IdMapper> idMappers, String endPoint) {

        if(!isCallerCCS(serviceName, tsId)) {
            return false;
        }

        List<String> baseCurrencyDps = dps.stream().filter(dp -> DP_BASE_CURRENCY.equals(dp) || DP_INCEPTION_DATE.equals(dp)).collect(Collectors.toList());
        if(GRID_VIEW_ENDPOINT.equals(endPoint) && baseCurrencyDps.size() == dps.size()) {
            return true;
        }

        return TIME_SERIES_ENDPOINT.equals(endPoint) && !CollectionUtils.isEmpty(idMappers) && !idMappers.stream().anyMatch(idMapper -> idMapper.isPrivateId() || idMapper.isIndex());
    }

    public static boolean isCallerCCS(String serviceName, String tsId) {
        return CCS_SERVICE_NAME.equals(serviceName) && CCS_TS_ID.equals(tsId);
    }
}
