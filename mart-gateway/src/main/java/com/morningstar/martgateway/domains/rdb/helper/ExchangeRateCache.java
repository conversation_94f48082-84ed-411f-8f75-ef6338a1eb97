package com.morningstar.martgateway.domains.rdb.helper;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.ExchangeRateLoader;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.martgateway.util.parsefunction.ParseUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.lang.Math.abs;


@Slf4j
public class ExchangeRateCache {

    private static final String EXCHANGE_RATE_DP = "HS459";

    private static final long EPOC_START_DATE_DIFF = abs(LocalDate.of(1900, 1, 1).toEpochDay());

    private ExchangeRateLoader exchangeRateLoader;

    private Cache<String, List<CacheValue>> dataCache;

    public ExchangeRateCache(ExchangeRateLoader exchangeRateLoader, long count) {
        this.exchangeRateLoader = exchangeRateLoader;
        dataCache = Caffeine.newBuilder()
                .maximumSize(count)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();
    }

    public Flux<Result> getExchangeRatesData(MartRequest martRequest) {
        boolean hasCurrencyDp = martRequest.getDps().stream().anyMatch(EXCHANGE_RATE_DP::equals);
        if(hasCurrencyDp) {
            List<String> currencyIds = martRequest.getIds().stream().filter(id -> id.startsWith("CU$$$$$")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(currencyIds)) {
                if(martRequest.getDps().size() == 1) {
                    martRequest.getIds().removeIf(currencyIds::contains);
                }
                return getExchangeRatesData(EXCHANGE_RATE_DP, currencyIds, martRequest.getStartDate(), martRequest.getEndDate());
            }
        }

        return Flux.empty();
    }

    private Flux<Result> getExchangeRatesData(String dataPoint, List<String> currencyIds, String startDate, String endDate) {

        return Flux.fromIterable(currencyIds).flatMap(currencyId -> {
            List<CacheValue> data = dataCache.getIfPresent(currencyId);
            if(data == null) {
                double[] rawData = exchangeRateLoader.getExchangeRates(currencyId, "1900-01-01", DateFormatUtil.format(LocalDate.now()));
                data = putCurrencyData(currencyId, rawData);
            }
            return convert(dataPoint, currencyId, data, startDate, endDate);
        });
    }

    private List<CacheValue>  putCurrencyData(String currency, double[] data) {

        List<CacheValue> currencyData = new ArrayList<>();
        for(int index = 0 ; index < data.length; index++) {
            double value = data[index];
            if(!Double.isNaN(value)) {
                currencyData.add(new CacheValue(index, new V(DateFormatUtil.format(LocalDate.ofEpochDay(index - EPOC_START_DATE_DIFF)), (String)ParseUtil.parseValue(value, "DOUBLE"))));
            }
        }

        dataCache.put(currency, currencyData);

        return currencyData;
    }

    private Flux<TimeSeriesResult> convert(String dataPoint, String currencyId, List<CacheValue> resultList, String startDate, String endDate) {

        long epocStartDate = LocalDate.parse(startDate).toEpochDay() + EPOC_START_DATE_DIFF;
        long epocEndDate = LocalDate.parse(endDate).toEpochDay() + EPOC_START_DATE_DIFF;

        List<V> values = resultList.stream().filter(p -> p.getEpocDate() >= epocStartDate && p.getEpocDate() <= epocEndDate).map(CacheValue::getValue).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(values)) {
            return Flux.empty();
        }

        Map<String, List<V>> data = new HashMap<>();
        data.put(dataPoint, values);

        return Flux.just(new TimeSeriesResult(currencyId, data));
    }

    @Data
    @AllArgsConstructor
    private class CacheValue {
        Integer epocDate;
        V value;
    }
}
