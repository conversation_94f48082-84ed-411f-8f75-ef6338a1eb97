package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.result.DataPointError;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.martgateway.domains.rdb.helper.ExchangeRateCache;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas;
import com.morningstar.martgateway.util.CCSEntitlementUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.morningstar.martgateway.util.CCSEntitlementUtil.TIME_SERIES_ENDPOINT;

@Slf4j
public class TsProtbufGatewayImp extends AbstractTsApiProxy implements MartGateway<TimeSeriesDatas, MartRequest> {

    private DataEntitlementService<MartRequest> dataEntitlementService;
    private EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService;

    private ExchangeRateCache exchangeRateCache;

    public TsProtbufGatewayImp(MartDataPipeline martDataPipeline, TsCacheProxyApplication tsCacheProxyApplication, IdMapUtil idMapUtil,
                               DataEntitlementService<MartRequest> dataEntitlementService,
                               EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService,
                               ExchangeRateCache exchangeRateCache) {
        super(martDataPipeline, tsCacheProxyApplication, idMapUtil);
        this.dataEntitlementService = dataEntitlementService;
        this.entitlementMartRequestFilterService = entitlementMartRequestFilterService;
        this.exchangeRateCache = exchangeRateCache;
    }

    @Override
    public Mono<TimeSeriesDatas> asyncRetrieveSecurities(MartRequest martRequest) {
        if (CollectionUtils.isEmpty(martRequest.getDps()) || CollectionUtils.isEmpty(martRequest.getIds())) {
            return Mono.empty();
        }

        martRequest.setIdMappers(this.idMapUtil.getIdMappers(martRequest.getListOfIds()));
        MartRequest originalMartRequest = martRequest.shallowCopy();
        Map<String, IdMapper> idMapperById = originalMartRequest.getIdMappers().stream().collect(Collectors.toMap(IdMapper::getInvestmentId, v -> v)); //use originalMartRequest so that even filtered IDs' id mappers can still be referenced.

        Flux<Result> exchangeRateData = exchangeRateCache.getExchangeRatesData(martRequest);
        if (CollectionUtils.isEmpty(martRequest.getIds())) {
            return buildTsResponse(exchangeRateData, idMapperById)
                    .subscribeOn(SchedulerConfiguration.getScheduler());
        }

        CachedEntitlement cachedEntitlement = null;
        boolean checkEntitlement = martRequest.isCheckEntitlement() && CCSEntitlementUtil.isCheckEntitlement(martRequest.getServiceName(), martRequest.getTsId(), martRequest.getDps(), martRequest.getIdMappers(), TIME_SERIES_ENDPOINT);
        FilteredRequestData<MartRequest> filteredRequestData = null;
        EntitlementMartRequestWrapper entitlementMartRequestWrapper = new EntitlementMartRequestWrapper(martRequest);
        if (checkEntitlement) {
            cachedEntitlement = dataEntitlementService.getEntitlement(martRequest.getUserId(), martRequest.getConfigId());

            filteredRequestData = entitlementMartRequestFilterService.filterRequest(
                    cachedEntitlement,
                    entitlementMartRequestWrapper);
            martRequest = filteredRequestData.getRequest();
        }

        TSRequest tsRequest = getTsCacheRequest(martRequest);
        Map<String, String> secIdRequestedIdMap = getTsSecIdMapAndPrepareRequests(martRequest, tsRequest);
        Flux<Result> tsData = getInputTsIdResult(getTsData(tsRequest), secIdRequestedIdMap);
        Flux<Result> rdbData = martDataPipeLine.execute(martRequest);

        Flux<Result> filteredResults;
        if (checkEntitlement) {
            filteredResults = Flux.fromIterable(filteredRequestData.getFilteredResults());
        } else {
            filteredResults = Flux.empty();
        }

        Flux<Result> mergedResultFlux = Flux.merge(rdbData, tsData, filteredResults, exchangeRateData);

        if (checkEntitlement) {
            mergedResultFlux = dataEntitlementService.applyDataEntitlement(mergedResultFlux, cachedEntitlement, new EntitlementMartRequestWrapper(originalMartRequest));
        }

        return buildTsResponse(mergedResultFlux, idMapperById)
                .subscribeOn(SchedulerConfiguration.getScheduler());
    }

    @Override
    public TsCacheDataForProtoBuf.TimeSeriesDatas syncRetrieveSecurities(MartRequest martRequest) {
        return asyncRetrieveSecurities(martRequest).block();
    }

    private Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> buildTsResponse(Flux<Result> results, Map<String, IdMapper> idMapperById) {
        return results.collectList()
                .map(resultList -> buildTimeSeriesDataFromResult(resultList, idMapperById));
    }

    private int formatDate(String date) {
        return (int) (LocalDate.parse(date, dateTimeFormatter).toEpochDay() - START_EPOCH_DAY);
    }

    private TsCacheDataForProtoBuf.TimeSeriesDatas buildTimeSeriesDataFromResult(List<Result> results, Map<String, IdMapper> idMapperById) {
        TsCacheDataForProtoBuf.TimeSeriesDatas.Builder timeSeriesDataBuilder = TsCacheDataForProtoBuf.TimeSeriesDatas.newBuilder();
        timeSeriesDataBuilder.setRetcode(0L).setMsg("");
        Status status = Status.OK;

        for (Result result : results) {
            if (result instanceof TimeSeriesResult) {
                Pair<List<TsCacheDataForProtoBuf.TimeSeriesData>, Status> timeSeriesDataAndErrorFlag = buildTimeSeriesDataFromTimeSeriesResult((TimeSeriesResult) result, idMapperById);
                timeSeriesDataBuilder.addAllValues(timeSeriesDataAndErrorFlag.getLeft());
                if (status == Status.OK) {
                    status = timeSeriesDataAndErrorFlag.getRight();
                }
            } else if (result instanceof TsResult) {
                Pair<List<TsCacheDataForProtoBuf.TimeSeriesData>, Status> tsDataAndStatus = buildTimeSeriesDataFromTsResult((TsResult) result, idMapperById);
                timeSeriesDataBuilder.addAllValues(tsDataAndStatus.getLeft());
                if (status == Status.OK) {
                    status = tsDataAndStatus.getRight();
                }
            } else if (result instanceof ErrorResult) {
                timeSeriesDataBuilder.addValues(buildTimeSeriesDataFromErrorResult((ErrorResult) result, idMapperById));
                status = Status.SUCCESS_WITH_DP_ERRORS;
            }
        }
        if(status != null && status != Status.OK){
            timeSeriesDataBuilder
                    .setRetcode(Long.parseLong(status.getCode()))
                    .setMsg(status.getMessage())
                    .build();
        }

        return timeSeriesDataBuilder.build();
    }

    private Pair<List<TsCacheDataForProtoBuf.TimeSeriesData>, Status> buildTimeSeriesDataFromTimeSeriesResult(TimeSeriesResult tsResult, Map<String, IdMapper> idMapperById) {
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = new ArrayList<>();
        Status status = Status.OK;
        String secId = buildSecId(tsResult.getId(), idMapperById);
        String[] secIdWithUniverse = secId.split(";");
        String secIdPart = secIdWithUniverse[0];
        String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;

        if (CollectionUtils.isNotEmpty(tsResult.getErrors())) {
            for (DataPointError error : tsResult.getErrors()) {
                TsCacheDataForProtoBuf.TimeSeriesData.Builder dataBuilder = TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
                        .setSecId(secIdPart)
                        .setDataId(error.getDatapointId())
                        .setErrorCode(error.getErrorCode());

                if (universe != null) {
                    dataBuilder.setUniverse(universe);
                }

                timeSeriesDataList.add(dataBuilder.build());
            }
            status = Status.SUCCESS_WITH_DP_ERRORS;
        } else {
            try {
                for (Map.Entry<String, List<V>> tsValuesForDp : tsResult.getValues().entrySet()) {
                    TsCacheDataForProtoBuf.TimeSeriesData.Builder dataBuilder = TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
                            .setSecId(secIdPart)
                            .setDataId(tsValuesForDp.getKey());

                    if (universe != null) {
                        dataBuilder.setUniverse(universe);
                    }

                    List<TsCacheDataForProtoBuf.TSValuePair> tsValuePairs = buildTsValuePairs(tsValuesForDp);
                    dataBuilder.addAllValues(tsValuePairs);
                    timeSeriesDataList.add(dataBuilder.build());
                }
            } catch (NumberFormatException e) {
                status = Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS;
            }
        }

        return Pair.of(timeSeriesDataList, status);
    }

    private Pair<List<TsCacheDataForProtoBuf.TimeSeriesData>, Status> buildTimeSeriesDataFromTsResult(TsResult tsResult, Map<String, IdMapper> idMapperById) {
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = new ArrayList<>();
        Status status = Status.OK;

        if (MapUtils.isEmpty(tsResult.getValues())) {
            return Pair.of(timeSeriesDataList, status);
        }

        String secId = buildSecId(tsResult.getId(), idMapperById);
        String[] secIdWithUniverse = secId.split(";");
        String secIdPart = secIdWithUniverse[0];
        String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;

        try {
            for (Map.Entry<String, TsDataProtoBuf.TSDataDouble> entry : tsResult.getValues().entrySet()) {
                String dataPointId = entry.getKey();
                TsDataProtoBuf.TSDataDouble tsDataDouble = entry.getValue();

                if (tsDataDouble.getDatesCount() != tsDataDouble.getValuesCount()) {
                    status = Status.SUCCESS_WITH_DP_ERRORS;
                    log.error("Dates and values count mismatch for datapoint {} in TsResult {}", dataPointId, tsResult.getId());
                    return Pair.of(timeSeriesDataList, status);
                }

                TsCacheDataForProtoBuf.TimeSeriesData.Builder dataBuilder = TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
                        .setSecId(secIdPart)
                        .setDataId(dataPointId);

                if (universe != null) {
                    dataBuilder.setUniverse(universe);
                }

                List<TsCacheDataForProtoBuf.TSValuePair> tsValuePairs = new ArrayList<>();
                int count = tsDataDouble.getDatesCount();

                for (int i = 0; i < count; i++) {
                    long dates = tsDataDouble.getDates(i) - START_EPOCH_DAY;
                    double value = tsDataDouble.getValues(i);

                    TsCacheDataForProtoBuf.TSValuePair.Builder pairBuilder = TsCacheDataForProtoBuf.TSValuePair.newBuilder()
                            .addDates(dates)
                            .addValues(value);

                    tsValuePairs.add(pairBuilder.build());
                }

                dataBuilder.addAllValues(tsValuePairs);
                timeSeriesDataList.add(dataBuilder.build());
            }
        } catch (NumberFormatException e) {
            status = Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS;
        }

        return Pair.of(timeSeriesDataList, status);
    }

    private TsCacheDataForProtoBuf.TimeSeriesData buildTimeSeriesDataFromErrorResult(ErrorResult errorResult, Map<String, IdMapper> idMapperById) {
        String secId = buildSecId(errorResult.getId(), idMapperById);
        String[] secIdWithUniverse = secId.split(";");
        String secIdPart = secIdWithUniverse[0];
        String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;

        TsCacheDataForProtoBuf.TimeSeriesData.Builder dataBuilder = TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
                .setSecId(secIdPart)
                .setDataId(errorResult.getDatapointId())
                .setErrorCode(errorResult.getErrorCode());

        if (universe != null) {
            dataBuilder.setUniverse(universe);
        }

        return dataBuilder.build();
    }

    private List<TsCacheDataForProtoBuf.TSValuePair> buildTsValuePairs(Map.Entry<String, List<V>> tsValues) {
        if (tsValues == null || tsValues.getValue() == null) {
            return Collections.emptyList();
        }
        return tsValues.getValue().stream()
                .map(v -> {
                    TsCacheDataForProtoBuf.TSValuePair.Builder pairBuilder = TsCacheDataForProtoBuf.TSValuePair.newBuilder()
                            .addDates(formatDate(v.getI()));
                    if (StringUtils.isNotEmpty(v.getV())) {
                        pairBuilder.addValues(Double.parseDouble(v.getV()));
                    }
                    return pairBuilder.build();
                })
                .collect(Collectors.toList());
    }
}
