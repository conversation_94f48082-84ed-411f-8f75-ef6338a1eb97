package com.morningstar.martgateway.interfaces;

import static com.morningstar.martgateway.domains.calc.CustomCalcRequestUtil.buildMartRequestsForCustomCalc;
import static com.morningstar.martgateway.domains.calc.CustomCalcRequestUtil.filterCustomCalcDataPoints;

import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.martgateway.applications.apiproxy.DirectApiGateway;
import com.morningstar.martgateway.domains.apiproxy.entity.Transforms;
import com.morningstar.martgateway.applications.delta.DeltaGateway;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.DataPointSource;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementInvestmentApiRequestWrapper;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.martgateway.interfaces.model.FormatConverter;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.martgateway.util.CCSEntitlementUtil;
import com.morningstar.martgateway.util.SecurityResponseToResultMapper;
import com.morningstar.martgateway.util.apiproxy.DataPointSourceUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class GridViewGatewayImpl implements MartGateway<InvestmentResponse, InvestmentApiRequest> {

    private final MartDataPipeline martDataPipeLine;
    private final DataEntitlementService<InvestmentApiRequest> dataEntitlementService;
    private final EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService;
    private final DirectApiGateway directApiGateway;
    private final DeltaGateway deltaGateway;
    private final DataPointSourceUtil dataPointSourceUtil;
    private final IdMapUtil idMapUtil;
    private final FormatConverter formatConverter;
    private final Boolean useNewCCS;

    public GridViewGatewayImpl(
            MartDataPipeline martDataPipeLine,
            DataEntitlementService<InvestmentApiRequest> dataEntitlementService,
            EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService,
            DirectApiGateway directApiGateway,
            DeltaGateway deltaGateway,
            DataPointSourceUtil dataPointSourceUtil,
            IdMapUtil idMapUtil, FormatConverter formatConverter, Boolean useNewCCS
    ) {
        this.martDataPipeLine = martDataPipeLine;
        this.dataEntitlementService = dataEntitlementService;
        this.entitlementInvestmentApiRequestFilterService = entitlementInvestmentApiRequestFilterService;
        this.directApiGateway = directApiGateway;
        this.deltaGateway = deltaGateway;
        this.dataPointSourceUtil = dataPointSourceUtil;
        this.idMapUtil = idMapUtil;
        this.formatConverter = formatConverter;
        this.useNewCCS = useNewCCS;
    }

    @Override
    public Mono<InvestmentResponse> asyncRetrieveSecurities(InvestmentApiRequest request) {
        if (!request.validateRequest()) {
            return Mono.just(new InvestmentResponse(Status.BAD_REQUEST, null));
        }
        request.reformatIdWithUniverseOnly();
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), StringUtils.isEmpty(request.getRequestId()) ? UUID.randomUUID().toString() : request.getRequestId());

        InvestmentApiRequest originalRequest = request.shallowCopy(); // keep an unfiltered / unmodified copy of the original request.
        List<GridviewDataPoint> allDataPointsCopy = originalRequest.getAllDataPoints();
        List<IdMapper> initialIdMapperList = idMapUtil.getIdMappers(request.getAllInvestmentIds());
        removeUnmatchedInvestmentId(request, initialIdMapperList);
        // initialize idMappers when no idtype is provided, which can we used and modified by pre filter and then later use for both martRequests and in dataEntitlementService.applyDataEntitlement
        if(StringUtils.isEmpty(request.getIdType())) {
            request.setIdMappers(initialIdMapperList);
        }
        CachedEntitlement cachedEntitlement = null;
        boolean checkEntitlement = request.isCheckEntitlement() && CCSEntitlementUtil.isCheckEntitlement(request.getServiceName(), request.getTsId(),
                request.getDataPoints().stream().map(GridviewDataPoint::getDataPointId).collect(Collectors.toList()), request.getIdMappers(), CCSEntitlementUtil.GRID_VIEW_ENDPOINT);

        EntitlementInvestmentApiRequestWrapper entitlementInvestmentApiRequestWrapper = new EntitlementInvestmentApiRequestWrapper(request);
        FilteredRequestData<InvestmentApiRequest> filteredRequestData = null;
        if (checkEntitlement) {
            cachedEntitlement = dataEntitlementService.getEntitlement(request.getUserId(), request.getConfigId());
            filteredRequestData = entitlementInvestmentApiRequestFilterService.filterRequest(
                    cachedEntitlement, entitlementInvestmentApiRequestWrapper);
            request = filteredRequestData.getRequest();
        }

        Map<DataPointSource, List<GridviewDataPoint>> dataPointsPerSource = dataPointSourceUtil.splitDataPointsPerSource(allDataPointsCopy, request.getProductId(),request.isSkipProxy());

        request.setDataPoints(dataPointsPerSource.get(DataPointSource.INVESTMENT_API));

        List<MartRequest> martRequestList = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(request.getDataPoints())) {
            martRequestList = buildMartRequests(request);
        }

        if (request.isDeltaRequest()) {
            return handleDeltaRequest(request, cachedEntitlement, originalRequest, allDataPointsCopy);
        }

        Flux<Result> resultFlux = Flux.empty();
        boolean hasData = false;

        if (CollectionUtils.isNotEmpty(martRequestList)) {
            resultFlux = Flux.fromIterable(martRequestList).flatMap(martDataPipeLine::execute);
            hasData = true;
        }
        Flux<Result> directResultFlux = Flux.empty();

        if (CollectionUtils.isNotEmpty(dataPointsPerSource.get(DataPointSource.DIRECT_API))) {
            directResultFlux = getDirectResults(request, dataPointsPerSource.get(DataPointSource.DIRECT_API));
            hasData = true;
        }

        Flux<Result> filteredResults;
        if (checkEntitlement) {
            filteredResults = Flux.fromIterable(filteredRequestData.getFilteredResults());
        } else {
            filteredResults = Flux.empty();
        }

        Flux<Result> mergedResultFlux = Flux.merge(resultFlux, directResultFlux, filteredResults);

        if (hasData && checkEntitlement) {
            mergedResultFlux = dataEntitlementService.applyDataEntitlement(mergedResultFlux, cachedEntitlement, new EntitlementInvestmentApiRequestWrapper(originalRequest));
        }

        return formatResponse(mergedResultFlux, allDataPointsCopy).subscribeOn(SchedulerConfiguration.getScheduler());
    }

    private List<MartRequest> buildMartRequests(InvestmentApiRequest request) {
        if (request.isDeltaRequest()) {
            return Collections.emptyList();
        }
        List<MartRequest> martRequests = new ArrayList<>();
        List<GridviewDataPoint> customCalcDataPoints = filterCustomCalcDataPoints(request, useNewCCS);
        MartRequest customCalcMartRequest = buildMartRequestsForCustomCalc(request, customCalcDataPoints);
        if(customCalcMartRequest != null) {
            martRequests.add(customCalcMartRequest);
        }

        martRequests.addAll(request.transformToMartRequests(useNewCCS));
        return martRequests;
    }

    private Mono<InvestmentResponse> handleDeltaRequest(InvestmentApiRequest request, CachedEntitlement cachedEntitlement, InvestmentApiRequest originalRequest, List<GridviewDataPoint> allDataPointsCopy) {
        Flux<Result> deltaResultFlux = deltaGateway.retrieve(request);
        if (request.isCheckEntitlement()) {
            deltaResultFlux = dataEntitlementService.applyDataEntitlement(deltaResultFlux, cachedEntitlement, new EntitlementInvestmentApiRequestWrapper(originalRequest));
        }
        return formatResponse(deltaResultFlux, allDataPointsCopy).subscribeOn(SchedulerConfiguration.getScheduler());
    }

    private void removeUnmatchedInvestmentId(InvestmentApiRequest request, List<IdMapper> idMappers) {
        Set<String> investmentIds = idMappers.stream()
                .map(IdMapper::getInvestmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (StringUtils.isNotEmpty(request.getIdType())) {
            request.getInvestments().removeIf(investment -> investmentIds.contains(investment.getId()));
        }
    }

    private Mono<InvestmentResponse> formatResponse(Flux<Result> resultFlux, List<GridviewDataPoint> allDataPoints) {
        if (allDataPoints.stream().anyMatch(dp -> CodeMappings.hasCodeMapping(dp.getDataPointId()))) {
            return formatConverter.formatInvestmentResponse(resultFlux, buildAliasToDpIdMap(allDataPoints));
        } else {
            return formatConverter.formatInvestmentResponse(resultFlux);
        }
    }

    private Map<String, String> buildAliasToDpIdMap(List<GridviewDataPoint> dataPoints) {
        Map<String, String> aliasToDpIdMap = new HashMap<>(dataPoints.size());
        for (GridviewDataPoint dataPoint : dataPoints) {
            String alias = StringUtils.isEmpty(dataPoint.getAlias()) ? dataPoint.getDataPointId() : dataPoint.getAlias();
            aliasToDpIdMap.put(alias, dataPoint.getDataPointId());
        }
        return aliasToDpIdMap;
    }

    private Flux<Result> getDirectResults(InvestmentApiRequest investmentApiRequest, List<GridviewDataPoint> directDataPoints) {
        if (directDataPoints == null || directDataPoints.isEmpty()) {
            return Flux.empty();
        }
        Map<String, String> aliasToDataFormatMap = directDataPoints.stream()
                .filter(dataPoint -> dataPoint.getDateFormat() != null)
                .collect(Collectors.toMap(GridviewDataPoint::getAlias, GridviewDataPoint::getDateFormat));
        return directApiGateway
                .asyncRetrieveSecurities(Transforms.transformToSecuritiesRequestEntity(investmentApiRequest,directDataPoints))
                .map(x -> SecurityResponseToResultMapper.toResult(x, aliasToDataFormatMap))
                .flatMapMany(Flux::fromIterable);
    }

    @Override
    public InvestmentResponse syncRetrieveSecurities(InvestmentApiRequest investmentApiRequest) {
        return asyncRetrieveSecurities(investmentApiRequest).block();
    }
}