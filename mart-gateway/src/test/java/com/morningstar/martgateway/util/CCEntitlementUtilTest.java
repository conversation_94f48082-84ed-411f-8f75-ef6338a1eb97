package com.morningstar.martgateway.util;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import org.apache.commons.collections4.ListUtils;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.List;

import static com.morningstar.martgateway.util.CCSEntitlementUtil.CCS_SERVICE_NAME;
import static com.morningstar.martgateway.util.CCSEntitlementUtil.CCS_TS_ID;
import static com.morningstar.martgateway.util.CCSEntitlementUtil.DP_BASE_CURRENCY;
import static com.morningstar.martgateway.util.CCSEntitlementUtil.DP_INCEPTION_DATE;
import static com.morningstar.martgateway.util.CCSEntitlementUtil.GRID_VIEW_ENDPOINT;
import static com.morningstar.martgateway.util.CCSEntitlementUtil.TIME_SERIES_ENDPOINT;

@RunWith(MockitoJUnitRunner.class)
public class CCEntitlementUtilTest {

    @Mock
    private LocalIdMapperCache localIdMapperCache;

    private IdMapUtil idMapUtil;


    @Before
    public void setUp() {
        idMapUtil = new IdMapUtil(localIdMapperCache);
    }

    @Test
    public void testTsCall() {
        Assert.assertEquals(true, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), TIME_SERIES_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), GRID_VIEW_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS("ZZZ", CCS_TS_ID, List.of("HPD10"), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), TIME_SERIES_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), idMapUtil.buildIdMappers("XI", List.of("XIGBR05U0X")), TIME_SERIES_ENDPOINT));
    }

    @Test
    public void testBaseCurrencyCall() {
        Assert.assertEquals(true, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of(DP_INCEPTION_DATE), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), GRID_VIEW_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, "DIRECT", List.of(DP_INCEPTION_DATE), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), GRID_VIEW_ENDPOINT));
        Assert.assertEquals(true, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of(DP_INCEPTION_DATE), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), TIME_SERIES_ENDPOINT));
        Assert.assertEquals(true, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of(DP_BASE_CURRENCY, DP_INCEPTION_DATE), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), GRID_VIEW_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of(DP_BASE_CURRENCY, DP_INCEPTION_DATE, "12345"), idMapUtil.buildIdMappers("FO", List.of("FOGBR05U0X")), GRID_VIEW_ENDPOINT));
    }

    @Test
    public void testPMShouldCheckEntitlement() {

        JSONObject jsonObject = new JSONObject()
                .put("SecId", "FOGBR05U0X")
                .put("SecurityType", "FO")
                .put("Status", "1");

        Assert.assertEquals(true, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), List.of(new NonEmptyIdMapper("FOGBR05U0X", jsonObject)), TIME_SERIES_ENDPOINT));
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), null, TIME_SERIES_ENDPOINT));

        jsonObject.put("Status", "254");
        Assert.assertEquals(false, CCSEntitlementUtil.canBypassForCCS(CCS_SERVICE_NAME, CCS_TS_ID, List.of("HPD10"), List.of(new NonEmptyIdMapper("FOGBR05U0X", jsonObject)), TIME_SERIES_ENDPOINT));
    }
}

