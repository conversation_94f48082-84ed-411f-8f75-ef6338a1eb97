package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.calculationlib.conversion.ExchangeRateLoader;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.DataPointError;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.martgateway.domains.rdb.helper.ExchangeRateCache;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@MockitoSettings(strictness = Strictness.LENIENT)
class TsProtbufGatewayImpTest {

    // ========== Test Dependencies ==========
    @Mock
    private MartDataPipeline martDataPipeline;
    @Mock
    private TsCacheProxyApplication tsCacheProxyApplication;
    @Mock
    private IdMapUtil idMapUtil;
    @Mock
    private DataEntitlementService<MartRequest> dataEntitlementService;
    @Mock
    private EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService;
    @Mock
    private ExchangeRateLoader exchangeRateLoader;

    private ExchangeRateCache exchangeRateCache;
    private TsProtbufGatewayImp tsProtbufGatewayImp;

    // ========== Test Setup ==========
    @BeforeEach
    void setup() {
        exchangeRateCache = new ExchangeRateCache(exchangeRateLoader, 10000);
        this.tsProtbufGatewayImp = new TsProtbufGatewayImp(martDataPipeline, tsCacheProxyApplication, idMapUtil,
                dataEntitlementService, entitlementMartRequestFilterService, exchangeRateCache);
        DataPointRepository.setDataPointMap(buildDpMap());
    }

    // ========== Basic Functionality Tests ==========

    @Test
    public void syncRetrieveSecurities_WithValidData_ShouldReturnCorrectResponse() {
        MartRequest martRequest = setupData();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // Verify all data belongs to correct security
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertTrue(timeSeriesDataList.stream().allMatch(data -> "5PUSA00003".equals(data.getSecId())));

        // Verify specific datapoint values
        verifyDataPointValue(actualResponse, "5PUSA00003", "190012", 500.00);
        verifyDataPointValue(actualResponse, "5PUSA00003", "HP010", 100.00);
        verifyDataPointValue(actualResponse, "5PUSA00003", "HP012", 105.05);
    }

    @Test
    public void syncRetrieveSecurities_WithEmptyData_ShouldReturnEmptyResponse() {
        MartRequest martRequest = setupEmptyData();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
    }

    @Test
    public void syncRetrieveSecurities_WithMostRecentData_ShouldHandleCurrentResult() {
        MartRequest martRequest = setupMostRecentData();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
    }


    private MartRequest setupData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Collections.singletonList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private MartRequest setupEmptyData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Collections.singletonList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        when(martDataPipeline.execute(any())).thenReturn(Flux.empty());
        return martRequest;
    }

    private MartRequest setupMostRecentData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Collections.singletonList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, String> currentValues = new HashMap<>();
        currentValues.put("190012", "500.00");
        CurrentResult result = new CurrentResult("5PUSA00003", currentValues);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }



    private TSResponse buildTsResponse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        TSContent tsContent = new TSContent();

        TSItem tsItem = new TSItem();
        tsItem.setSecid("5PUSA00003;CP");
        tsItem.setDataid("HP010");
        TSData tsData = new TSData();
        tsData.setDate(10012);
        tsData.setValue(100.00);
        tsItem.setData(Collections.singletonList(tsData));

        TSItem tsItem2 = new TSItem();
        tsItem2.setSecid("5PUSA00003;CP");
        tsItem2.setDataid("HP012");
        TSData tsData2 = new TSData();
        tsData2.setDate(10015);
        tsData2.setValue(105.05);
        tsItem2.setData(Collections.singletonList(tsData2));

        tsContent.setItems(List.of(tsItem, tsItem2));
        tsResponse.setContent(tsContent);
        return tsResponse;
    }

    private Map<String, DataPoint> buildDpMap() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        DataPoint dataPoint1 = DataPoint.builder().nid("HP010").id("HP010").src("CDAPI").tsRdb(RdbDataPoint.builder().build()).build();
        DataPoint dataPoint2 = DataPoint.builder().nid("190012").id("190012").contentType("decimal").src("RDB").tsRdb(RdbDataPoint.builder().build()).build();
        dataPointMap.put("HP010", dataPoint1);
        dataPointMap.put("190012", dataPoint2);
        return dataPointMap;
    }

    // ========== Entitlement Tests ==========

    @Test
    public void syncRetrieveSecurities_WhenNoUserEntitlement_ShouldGetResponse() {
        MartRequest martRequest = MartRequest.builder()
                .ids(Collections.singletonList("F00001IIYM"))
                .dps(Collections.singletonList("3010"))
                .userId("user_id")
                .checkEntitlement(true)
                .build();
        Map<String, List<V>> values = new HashMap<>();
        values.put("AU002", Collections.singletonList(new V("2023-01-01","500.00")));

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenThrow(new NullPointerException());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values)));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class)))
                .thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values),
                        new ErrorResult("F00001IIYM", "HPD10", "403"),
                        new ErrorResult("5PUSA00003", "HPD10", "403")));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(martRequest));

        TsCacheDataForProtoBuf.TimeSeriesDatas response = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(response);
    }

    @Test
    public void syncRetrieveSecurities_WithEntitlementFiltered_ShouldReturnFilteredResponse() {
        MartRequest martRequest = MartRequest.builder()
                .ids(new ArrayList<>(Arrays.asList("F00001IIYM", "5PUSA00003")))
                .dps(new ArrayList<>(Arrays.asList("AU002", "HPD10")))
                .checkEntitlement(true)
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        Map<String, List<V>> values = new HashMap<>();
        values.put("AU002", Collections.singletonList(new V("2023-01-01","500.00")));
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values)));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class)))
                .thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values),
                        new ErrorResult("F00001IIYM", "HPD10", "403"),
                        new ErrorResult("5PUSA00003", "HPD10", "403")));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(martRequest));

        TsCacheDataForProtoBuf.TimeSeriesDatas response = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertEquals(200200L, response.getRetcode());

        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(response);
        assertEquals(3, timeSeriesDataList.size());

        // Verify successful data retrieval
        verifyDataPointValue(response, "F00001IIYM", "AU002", 500.0);

        // Verify error responses
        verifyErrorResponse(response, "F00001IIYM", "HPD10", "403");
        verifyErrorResponse(response, "5PUSA00003", "HPD10", "403");
    }

    @Test
    public void syncRetrieveSecurities_WhenEntitlementFails_ShouldThrowException() {
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of("F00001IIYM"))
                .dps(List.of("3010"))
                .userId("testUser")
                .configId("testConfig")
                .checkEntitlement(true)
                .build();

        EntitlementException expectedException = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);
        when(dataEntitlementService.getEntitlement(martRequest.getUserId(), martRequest.getConfigId()))
                .thenThrow(expectedException);

        try {
            tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        } catch (EntitlementException e) {
            assertEquals("Redis connection failed", e.getMessage());
        }

        // Verify no downstream processing occurred
        verify(martDataPipeline, never()).execute(any());
        verify(tsCacheProxyApplication, never()).retrieve(any());
    }

    private MartRequest setupDataWithInvalidDataType(){

        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP026,190240"))
                .ids(Collections.singletonList("F00000S7JB"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("F00000S7JB", "{\"SecurityType\":\"FO\",\"SecId\":\"F00000S7JB\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190240", Collections.singletonList(new V("2023-01-01","Alex Lucas")));

        Map<String, List<V>> values2 = new HashMap<>();
        values2.put("HP026", Collections.singletonList(new V("2023-12-31","1600.00")));
        TimeSeriesResult result = new TimeSeriesResult("F00000S7JB", values);
        TimeSeriesResult result2 = new TimeSeriesResult("F00000S7JB", values2);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result, result2));
        return martRequest;
    }

    // ========== Error Handling Tests ==========

    @Test
    public void syncRetrieveSecurities_WithInvalidDataType_ShouldReturnErrorStatus() {
        MartRequest martRequest = setupDataWithInvalidDataType();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        assertEquals(Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS.getCode(), String.valueOf(actualResponse.getRetcode()));

        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertTrue(timeSeriesDataList.stream().allMatch(data -> "F00000S7JB".equals(data.getSecId())));

        // Verify valid datapoint still returns data
        verifyDataPointValue(actualResponse, "F00000S7JB", "HP026", 1600.00);
    }

    // ========== Edge Cases and Boundary Conditions ==========

    @Test
    public void syncRetrieveSecurities_WithEmptyDpsAndIds_ShouldReturnNull() {

        MartRequest emptyDpsRequest = MartRequest.builder()
                .dps(Collections.emptyList())
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        TsCacheDataForProtoBuf.TimeSeriesDatas response1 = tsProtbufGatewayImp.syncRetrieveSecurities(emptyDpsRequest);
        assertNull(response1); // Mono.empty().block() returns null

        // Test empty ids
        MartRequest emptyIdsRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.emptyList())
                .build();

        TsCacheDataForProtoBuf.TimeSeriesDatas response2 = tsProtbufGatewayImp.syncRetrieveSecurities(emptyIdsRequest);
        assertNull(response2); // Mono.empty().block() returns null
        
        MartRequest nullDpsRequest = MartRequest.builder()
                .dps(null)
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        TsCacheDataForProtoBuf.TimeSeriesDatas response3 = tsProtbufGatewayImp.syncRetrieveSecurities(nullDpsRequest);
        assertNull(response3); // Mono.empty().block() returns null
        
        MartRequest nullIdsRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(null)
                .build();

        TsCacheDataForProtoBuf.TimeSeriesDatas response4 = tsProtbufGatewayImp.syncRetrieveSecurities(nullIdsRequest);
        assertNull(response4); // Mono.empty().block() returns null
    }

    // ========== TsResult Processing Tests ==========

    @Test
    public void syncRetrieveSecurities_WithTsResult_ShouldProcessCorrectly() {
        MartRequest martRequest = setupTsResultData();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertFalse(timeSeriesDataList.isEmpty());

        TsCacheDataForProtoBuf.TimeSeriesData tsData = findTimeSeriesData(actualResponse, "TEST001", "DP001");
        assertNotNull(tsData);
        assertEquals("TEST001", tsData.getSecId());
        assertEquals("DP001", tsData.getDataId());
        assertFalse(tsData.getValuesList().isEmpty());
    }

    @Test
    public void syncRetrieveSecurities_WithEmptyTsResult_ShouldHandleGracefully() {
        MartRequest martRequest = setupTsResultWithEmptyValues();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        assertEquals(0L, actualResponse.getRetcode());
    }

    @Test
    public void testTsResultWithDateValueMismatch() {
        MartRequest martRequest = setupTsResultWithDateValueMismatch();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
        
        assertEquals(Status.SUCCESS_WITH_DP_ERRORS.getCode(), String.valueOf(actualResponse.getRetcode()));
    }

    @Test
    public void testTimeSeriesResultWithErrors() {
        MartRequest martRequest = setupTimeSeriesResultWithErrors();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
        
        assertEquals(0L, actualResponse.getRetcode());
    }

    @Test
    public void testSecIdWithUniverse() {
        MartRequest martRequest = setupDataWithUniverse();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertFalse(timeSeriesDataList.isEmpty());
        
        TsCacheDataForProtoBuf.TimeSeriesData dataWithUniverse = timeSeriesDataList.get(0);
        assertEquals("CP", dataWithUniverse.getUniverse());
        assertEquals("5PUSA00003", dataWithUniverse.getSecId());
    }

    @Test
    public void testBuildTsValuePairsWithNullValues() {
        MartRequest martRequest = setupDataWithNullValues();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
        
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertFalse(timeSeriesDataList.isEmpty());
    }

    @Test
    public void testStatusHandlingInBuildTimeSeriesDataFromResult() {
        MartRequest martRequest = setupMixedResultTypes();
        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // When there are errors, status should be updated appropriately
        assertEquals(Status.SUCCESS_WITH_DP_ERRORS.getCode(), String.valueOf(actualResponse.getRetcode()));
        assertEquals(Status.SUCCESS_WITH_DP_ERRORS.getMessage(), actualResponse.getMsg());
    }

    private MartRequest setupTsResultData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("DP001"))
                .ids(Collections.singletonList("TEST001"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("TEST001", "{\"SecurityType\":\"EQ\",\"SecId\":\"TEST001\"}")));

        // Create TsResult with TsDataProtoBuf.TSDataDouble
        TsDataProtoBuf.TSDataDouble tsDataDouble = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("TEST001")
                .setDpId("DP001")
                .addDates(44927L) // 2023-01-01 in epoch days from 1900-01-01
                .addDates(44928L) // 2023-01-02
                .addValues(100.0)
                .addValues(101.0)
                .build();

        Map<String, TsDataProtoBuf.TSDataDouble> tsValues = new HashMap<>();
        tsValues.put("DP001", tsDataDouble);
        TsResult tsResult = new TsResult("TEST001", tsValues);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult));
        return martRequest;
    }

    private MartRequest setupTsResultWithEmptyValues() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("DP001"))
                .ids(Collections.singletonList("TEST001"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("TEST001", "{\"SecurityType\":\"EQ\",\"SecId\":\"TEST001\"}")));

        // Create TsResult with empty values
        TsResult tsResult = new TsResult("TEST001", new HashMap<>());

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult));
        return martRequest;
    }

    private MartRequest setupTsResultWithDateValueMismatch() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("DP001"))
                .ids(Collections.singletonList("TEST001"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("TEST001", "{\"SecurityType\":\"EQ\",\"SecId\":\"TEST001\"}")));

        // Create TsResult with mismatched dates and values count
        TsDataProtoBuf.TSDataDouble tsDataDouble = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("TEST001")
                .setDpId("DP001")
                .addDates(44927L) // 1 date
                .addDates(44928L) // 2 dates
                .addValues(100.0) // but only 1 value - mismatch!
                .build();

        Map<String, TsDataProtoBuf.TSDataDouble> tsValues = new HashMap<>();
        tsValues.put("DP001", tsDataDouble);
        TsResult tsResult = new TsResult("TEST001", tsValues);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult));
        return martRequest;
    }

    private MartRequest setupTimeSeriesResultWithErrors() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("DP001"))
                .ids(Collections.singletonList("TEST001"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("TEST001", "{\"SecurityType\":\"EQ\",\"SecId\":\"TEST001\"}")));
        
        TimeSeriesResult tsResult = new TimeSeriesResult("TEST001", new HashMap<>());

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult));
        return martRequest;
    }

    private MartRequest setupDataWithUniverse() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponseWithUniverse()));
        when(martDataPipeline.execute(any())).thenReturn(Flux.empty());
        return martRequest;
    }

    private MartRequest setupDataWithNullValues() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));

        Map<String, List<V>> values = new HashMap<>();
        List<V> vList = new ArrayList<>();
        vList.add(new V("2023-01-01", "100.0"));
        vList.add(new V("2023-01-02", null)); // null value
        vList.add(new V("2023-01-03", "")); // empty value
        values.put("HP010", vList);

        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }



    private MartRequest setupMixedResultTypes() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010", "DP001"))
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        
        Map<String, List<V>> values = new HashMap<>();
        values.put("HP010", Collections.singletonList(new V("2023-01-01", "100.0")));
        TimeSeriesResult tsResult = new TimeSeriesResult("5PUSA00003", values);

        ErrorResult errorResult = new ErrorResult("5PUSA00003", "DP001", "404");

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult, errorResult));
        return martRequest;
    }

    private TSResponse buildTsResponseWithUniverse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        TSContent tsContent = new TSContent();

        TSItem tsItem = new TSItem();
        tsItem.setSecid("5PUSA00003;CP"); // Include universe in secid
        tsItem.setDataid("HP010");
        TSData tsData = new TSData();
        tsData.setDate(10012);
        tsData.setValue(100.00);
        tsItem.setData(Collections.singletonList(tsData));

        tsContent.setItems(Collections.singletonList(tsItem));
        tsResponse.setContent(tsContent);
        return tsResponse;
    }

    @Test
    public void testFormatDateMethod() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));

        Map<String, List<V>> values = new HashMap<>();
        values.put("HP010", Collections.singletonList(new V("2023-01-01", "100.0")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));

        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // Verify date formatting is working correctly
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertFalse(timeSeriesDataList.isEmpty());

        TsCacheDataForProtoBuf.TimeSeriesData data = timeSeriesDataList.get(0);
        assertFalse(data.getValuesList().isEmpty());

        TsCacheDataForProtoBuf.TSValuePair valuePair = data.getValues(0);
        assertTrue(valuePair.getDatesCount() > 0);
        assertTrue(valuePair.getValuesCount() > 0);
    }

    @Test
    public void testNumberFormatExceptionInBuildTsValuePairs() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.singletonList("5PUSA00003"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));

        Map<String, List<V>> values = new HashMap<>();
        values.put("HP010", Collections.singletonList(new V("2023-01-01", "invalid_number")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));

        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
        
        assertEquals(Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS.getCode(), String.valueOf(actualResponse.getRetcode()));
    }

    @Test
    public void testTsResultNumberFormatException() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("DP001"))
                .ids(Collections.singletonList("TEST001"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(
                new NonEmptyIdMapper("TEST001", "{\"SecurityType\":\"EQ\",\"SecId\":\"TEST001\"}")));

        // Create TsResult that will cause NumberFormatException during processing
        TsDataProtoBuf.TSDataDouble tsDataDouble = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("TEST001")
                .setDpId("DP001")
                .addDates(44927L)
                .addValues(Double.NaN) // This might cause issues during processing
                .build();

        Map<String, TsDataProtoBuf.TSDataDouble> tsValues = new HashMap<>();
        tsValues.put("DP001", tsDataDouble);
        TsResult tsResult = new TsResult("TEST001", tsValues);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(tsResult));

        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
    }

    @Test
    public void testEmptyIdsAfterExchangeRateFiltering() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.emptyList()) // Start with empty ids
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.emptyList());
        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.empty());

        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        // When ids are empty, the method returns Mono.empty(), so block() returns null
        assertNull(actualResponse);
    }

    @Test
    public void testBuildSecIdWithoutIdMapper() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Collections.singletonList("HP010"))
                .ids(Collections.singletonList("UNKNOWN_ID"))
                .build();

        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.emptyList());

        Map<String, List<V>> values = new HashMap<>();
        values.put("HP010", Collections.singletonList(new V("2023-01-01", "100.0")));
        TimeSeriesResult result = new TimeSeriesResult("UNKNOWN_ID", values);

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));

        TsCacheDataForProtoBuf.TimeSeriesDatas actualResponse = tsProtbufGatewayImp.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // Should handle case where no IdMapper is found
        List<TsCacheDataForProtoBuf.TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        if (!timeSeriesDataList.isEmpty()) {
            TsCacheDataForProtoBuf.TimeSeriesData data = timeSeriesDataList.get(0);
            assertEquals("UNKNOWN_ID", data.getSecId());
            assertTrue(data.getUniverse().isEmpty()); // No universe when no IdMapper
        }
    }

    // ========== Helper Methods ==========

    /**
     * Helper method to extract time series data list from response
     */
    private List<TsCacheDataForProtoBuf.TimeSeriesData> getTimeSeriesDataList(TsCacheDataForProtoBuf.TimeSeriesDatas response) {
        return response.getValuesList();
    }

    /**
     * Helper method to find specific time series data by secId and dataId
     */
    private TsCacheDataForProtoBuf.TimeSeriesData findTimeSeriesData(TsCacheDataForProtoBuf.TimeSeriesDatas response, String secId, String dataId) {
        return getTimeSeriesDataList(response).stream()
                .filter(data -> secId.equals(data.getSecId()) && dataId.equals(data.getDataId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * Helper method to get first value from time series data
     */
    private Double getFirstValue(TsCacheDataForProtoBuf.TimeSeriesData data) {
        if (data == null || data.getValuesList().isEmpty()) {
            return null;
        }
        TsCacheDataForProtoBuf.TSValuePair firstPair = data.getValues(0);
        return firstPair.getValuesCount() > 0 ? firstPair.getValues(0) : null;
    }

    /**
     * Helper method to get error code from time series data
     */
    private String getErrorCode(TsCacheDataForProtoBuf.TimeSeriesData data) {
        return data != null ? data.getErrorCode() : null;
    }

    // ========== Verification Methods ==========

    /**
     * Verify that a specific datapoint has the expected value
     */
    private void verifyDataPointValue(TsCacheDataForProtoBuf.TimeSeriesDatas response, String secId, String dataId, double expectedValue) {
        TsCacheDataForProtoBuf.TimeSeriesData data = findTimeSeriesData(response, secId, dataId);
        assertNotNull(data, String.format("Data not found for secId=%s, dataId=%s", secId, dataId));
        assertEquals(secId, data.getSecId());
        assertEquals(dataId, data.getDataId());
        assertEquals(expectedValue, getFirstValue(data), 0.01);
    }

    /**
     * Verify that a specific datapoint has an error response
     */
    private void verifyErrorResponse(TsCacheDataForProtoBuf.TimeSeriesDatas response, String secId, String dataId, String expectedErrorCode) {
        TsCacheDataForProtoBuf.TimeSeriesData data = findTimeSeriesData(response, secId, dataId);
        assertNotNull(data, String.format("Error data not found for secId=%s, dataId=%s", secId, dataId));
        assertEquals(secId, data.getSecId());
        assertEquals(dataId, data.getDataId());
        assertEquals(expectedErrorCode, getErrorCode(data));
    }

    // ========== Test Data Setup Methods ==========

    /**
     * Helper method to get filtered request data for entitlement tests
     */
    private static FilteredRequestData<MartRequest> getFilteredRequestData(MartRequest martRequest) {
        return new FilteredRequestData<>(martRequest, new ArrayList<>());
    }
}