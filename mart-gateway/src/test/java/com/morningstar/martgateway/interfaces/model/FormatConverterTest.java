package com.morningstar.martgateway.interfaces.model;

import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.DataPointError;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupDataTS;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSDatapointValueGroup;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSGroupResultValue;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.C;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.CurrentPair;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.G;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.GroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.SubgroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.TimeseriesData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.TimeseriesPair;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FormatConverterTest {

    private CurrentResult current1;
    private CurrentResult current2;
    private TimeSeriesResult tsResult;
    private GroupResult group;
    private List<V> v1List;
    private List<V> v2List;
    private List<CurrentPair> currentPairList1;
    private List<CurrentPair> currentPairList2;
    private GroupData groupData1;
    private InvestmentResponse investmentResponse;
    private G g;
    private R r1;
    private R r2;

    @BeforeAll
    public void setUp() {
        Map<String, String> currentMap1 = new HashMap<>();
        Map<String, String> currentMap2 = new HashMap<>();
        currentMap1.put("6353", "9.88903");
        currentMap2.put("3009", "CAN");
        current1 = new CurrentResult("0P0000XZQQ", currentMap1);
        current2 = new CurrentResult("0P0000XZQQ", currentMap2);

        List<CurrentResult> currentResultList = Arrays.asList(current1, current2);
        Map<String, List<CurrentResult>> groupMap = new HashMap<>();
        groupMap.put("17252", currentResultList);
        group = new GroupResult("0P00000F93", groupMap);
        V v1 = new V("6353", "9.88903");
        V v2 = new V("3009", "CAN");
        v1List = List.of(v1);
        v2List = List.of(v2);

        C c1 = new C(v1List);
        C c2 = new C(v2List);
        List<C> s = Arrays.asList(c1, c2);
        g = new G("17252", s);

        r1 = new R("0P0000XZQQ");
        r1.setC(Arrays.asList(v1, v2).stream().sorted().collect(Collectors.toList()));
        r2 = new R("0P00000F93");
        r2.setG(Collections.singletonList(g));

        Map<String, List<V>> tsMap = new HashMap<>();
        tsMap.put("dps1", v1List);
        tsResult = new TimeSeriesResult("secid1", tsMap);

        CurrentPair currentPair1 = new CurrentPair("6353", "9.88903", null, null);
        CurrentPair currentPair2 = new CurrentPair("3009", "CAN", null, null);
        currentPairList1 = List.of(currentPair1);
        currentPairList2 = List.of(currentPair2);

        List<CurrentPair> curr = new ArrayList<>();
        List<SubgroupData> subgroupDataList = new ArrayList<>();
        curr.add(currentPair1);
        curr.add(currentPair2);
        SubgroupData subgroupData1 = new SubgroupData(curr);
        subgroupDataList.add(subgroupData1);
        groupData1 = new GroupData("17252", subgroupDataList, null, null);

        Investment investment1 = new Investment("0P0000XZQQ");
        investment1.setCurrentPairList(Arrays.asList(currentPair1, currentPair2).stream().sorted().collect(Collectors.toList()));

        Investment investment2 = new Investment("0P00000F93");
        investment2.setGroupDataList(List.of(groupData1));

        investmentResponse = new InvestmentResponse(Status.OK, Arrays.asList(investment1, investment2));
    }

    @Test
    @Order(1)
    public void format() {
        Flux<R> flux = new FormatConverter()
                .format(Flux.fromIterable(Arrays.asList(current1, current2, group)));
        StepVerifier.create(flux)
                .expectSubscription()
                .expectNext(r1)
                .expectNext(r2)
                .verifyComplete();
    }

    @Test
    @Order(2)
    public void testTransform() {
        Flux testFlux = Flux.fromIterable(Arrays.asList(current1, current2, group)).groupBy(Result::getId)
                .flatMap(groupedFlux ->
                        groupedFlux.collectList()
                                .flatMapMany(resultList ->
                                        Flux.fromIterable(resultList).map(Result::transform)
                                ));
        StepVerifier.create(testFlux)
                .expectSubscription()
                .expectNext(v1List)
                .expectNext(v2List)
                .expectNext(Collections.singletonList(g)).verifyComplete();
    }

    @Test
    @Order(3)
    public void testFormatInvestmentResponse() {
        Mono<InvestmentResponse> investmentResponseMono = new FormatConverter()
                .formatInvestmentResponse(Flux.fromIterable(Arrays.asList(current1, current2, group, tsResult)));
        StepVerifier.create(investmentResponseMono)
                .assertNext(investmentResponse -> {
                    assertEquals(3, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("0P0000XZQQ".equals(investment.getId())) {
                            assertEquals(2, investment.getCurrentPairList().size(), "currentpair size");
                        } else if ("0P00000F93".equals(investment.getId())) {
                            assertEquals(1, investment.getGroupDataList().size(), "groupdata size");
                        } else if ("secid1".equals(investment.getId())) {
                            assertEquals(1, investment.getTimeseriesDataList().size(), "tsdata size");
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(4)
    public void testFormatInvestmentResponseCodeMappings() {
        Map<String, Map<String, String>> codeMappings = new HashMap<>();
        codeMappings.put("MMR01", Map.of("5", "Bronze", "6", "Silver", "7", "Gold"));
        CodeMappings.setCodeMappings(codeMappings);
        CurrentResult firstCurrent = new CurrentResult("F0000009IX", Map.of("MMR01_alias", "5"));
        CurrentResult secondCurrent = new CurrentResult("F0000009IX", Map.of("MMR01_alias", "6"));
        CurrentResult thirdCurrent = new CurrentResult("F0000009IX", Map.of("MMR01_alias", "7"));

        Mono<InvestmentResponse> investmentResponseMono = new FormatConverter().formatInvestmentResponse(
                Flux.fromIterable(Arrays.asList(firstCurrent, secondCurrent, thirdCurrent)),
                Map.of("MMR01_alias", "MMR01"));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("F0000009IX".equals(investment.getId())) {
                            assertEquals(3, investment.getCurrentPairList().size(), "currentpair size");
                            investment.getCurrentPairList().forEach(currentPair -> {
                                if ("5".equals(currentPair.getCode())) {
                                    assertEquals("Bronze", currentPair.getValue(), "Expect Bronze");
                                } else if ("6".equals(currentPair.getCode())) {
                                    assertEquals("Silver", currentPair.getValue(), "Expect Silver");
                                } else if ("7".equals(currentPair.getCode())) {
                                    assertEquals("Gold", currentPair.getValue(), "Expect Gold");
                                }
                            });
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(5)
    public void testFormatInvestmentResponseTimeSeriesCodeMappings() {
        Map<String, Map<String, String>> codeMappings = new HashMap<>();
        codeMappings.put("HS05A", Map.of("2", "Large Blend", "3", "Large Value"));
        CodeMappings.setCodeMappings(codeMappings);

        Map<String, List<V>> valueMap = new HashMap<>();
        List<V> values = List.of(new V("2024-04-30", "3"), new V("2024-05-31", "2"), new V("2000-04-30;2024-04-30", "3"), new V("2024-04-30", "3"));
        valueMap.put("HS05A", values);

        TimeSeriesResult result = new TimeSeriesResult("F00000WUF1", valueMap);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> investmentResponseMono =
                formatConverter.formatInvestmentResponse(Flux.fromIterable(List.of(result)), Map.of("HS05A", "HS05A"));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("F00000WUF1".equals(investment.getId())) {
                            assertEquals(1, investment.getTimeseriesDataList().size(), "result size");
                            List<TimeseriesPair> tsPairList = investment.getTimeseriesDataList().get(0).getTimeseriesPairList();
                            assertEquals("2024-04-30", tsPairList.get(0).getDate(), "date");
                            assertEquals(null, tsPairList.get(1).getEndDate(), "endDate");
                            assertEquals("2000-04-30", tsPairList.get(2).getStartDate(), "startDate");
                            assertEquals("2024-04-30", tsPairList.get(2).getEndDate(), "endDate");
                            assertEquals("2024-04-30", tsPairList.get(3).getDate(), "date");
                            assertEquals(null, tsPairList.get(3).getEndDate(), "endDate");
                            investment.getTimeseriesDataList().forEach(t -> {
                                assertEquals("HS05A", t.getDatapointId(), "Datapoint id");
                                t.getTimeseriesPairList().forEach(p -> {
                                    if ("2".equals(p.getCode())) {
                                        assertEquals("Large Blend", p.getValue(), "2: Large Blend");
                                    } else if ("2".equals(p.getCode())) {
                                        assertEquals("Large Value", p.getValue(), "3: Large Value");
                                    }
                                });
                            });
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(6)
    public void testFormatInvestmentResponseGroupSubDpCodeMappings() {
        Map<String, Map<String, String>> codeMappings = new HashMap<>();
        codeMappings.put("EQVIO", Map.of("ENG", "English", "ARA", "Arabic"));
        CodeMappings.setCodeMappings(codeMappings);
        GroupResult groupResult = new GroupResult("0P000000MQ", Map.of("EQGX8",
                List.of(new CurrentResult("EQGX8", Map.of("EQOVU", "HM 08", "EQVI0", "ENG", "EQM8H", "Pembroke")))));

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> investmentResponseMono =
                formatConverter.formatInvestmentResponse(Flux.fromIterable(List.of(groupResult)), Map.of("EQVI0", "EQVI0"));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "group result size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("0P000000MQ".equals(investment.getId())) {
                            assertEquals(1, investment.getGroupDataList().size(), "group result size");
                            investment.getGroupDataList().get(0).getSubGroupData().get(0).getValues().forEach(subDp -> {
                                if ("ENG".equals(subDp.getCode())) {
                                    assertEquals("English", subDp.getValue(), "Expect English");
                                }
                            });
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(7)
    public void testFormatInvestmentResponseWithErrors() {
        ErrorResult errorResult1 = new ErrorResult("F00000MF52;FO", "HP010", "403");
        ErrorResult errorResult2 = new ErrorResult("F00000AF98;FO", "HP020", "500");
        ErrorResult errorResult3 = new ErrorResult("F00000MF52;FO", "HP030", "500");
        ErrorResult errorResult4 = new ErrorResult(null, "HP040", "500");
        CurrentResult currentNoError = new CurrentResult("F00000MF52;FO", new HashMap<>() {{
            put("HP040", "someValue");
        }});
        CurrentResult currentWithError = new CurrentResult("F00000WUF1", new HashMap<>() {{
            put("HP040", "someValue");
        }});
        currentWithError.getErrors().add(new DataPointError("HP403", "403"));
        currentWithError.getErrors().add(new DataPointError("HP500", "500"));

        Mono<InvestmentResponse> investmentResponseMono = new FormatConverter()
                .formatInvestmentResponse(Flux.fromIterable(Arrays.asList(current1, errorResult1, current2, group, errorResult2, tsResult, errorResult3, errorResult4, currentNoError,currentWithError)));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(6, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("0P0000XZQQ".equals(investment.getId())) {
                            assertEquals(2, investment.getCurrentPairList().size(), "currentpair size");
                        } else if ("0P00000F93".equals(investment.getId())) {
                            assertEquals(1, investment.getGroupDataList().size(), "groupdata size");
                        } else if ("secid1".equals(investment.getId())) {
                            assertEquals(1, investment.getTimeseriesDataList().size(), "tsdata size");
                        } else if ("F00000MF52;FO".equals(investment.getId())) {
                            assertEquals(1, investment.getCurrentPairList().size(), "current size");
                            assertEquals(2, investment.getErrors().size(), "error size");
                        } else if ("F00000AF98;FO".equals(investment.getId())) {
                            assertEquals(0, investment.getCurrentPairList().size(), "current size");
                            assertEquals(1, investment.getErrors().size(), "error size");
                        } else if ("F00000WUF1".equals(investment.getId())) {
                            assertEquals(1, investment.getCurrentPairList().size(), "current size");
                            assertEquals(2, investment.getErrors().size(), "error size");
                        }
                    });
                    List<DataPointError> allErrors = investmentResponse
                            .getInvestments()
                            .stream().map(Investment::getErrors)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    assertEquals(5, allErrors.size(), "error size equals");
                    assertEquals(Status.SUCCESS_WITH_DP_ERRORS, investmentResponse.getStatus());
                })
                .verifyComplete();
    }

    @Test
    @Order(8)
    public void successWithDpErrorsWithoutErrorResults() {
        CurrentResult currentWithError = new CurrentResult("F00000WUF1", new HashMap<>() {{
            put("HP040", "someValue");
        }});
        currentWithError.getErrors().add(new DataPointError("HP403", "403"));
        currentWithError.getErrors().add(new DataPointError("HP403", "403"));

        Mono<InvestmentResponse> investmentResponseMono = new FormatConverter()
                .formatInvestmentResponse(Flux.fromIterable(List.of(currentWithError)));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("F00000WUF1".equals(investment.getId())) {
                            assertEquals(1, investment.getCurrentPairList().size(), "current size");
                            assertEquals(1, investment.getErrors().size(), "error size");
                        }
                    });
                    assertEquals(Status.SUCCESS_WITH_DP_ERRORS, investmentResponse.getStatus());
                })
                .verifyComplete();
    }

    @Test
    @Order(9)
    public void testFormatTsEquityInvestmentResponse() {

        List<V> values1 = new ArrayList<>();
        values1.add(new V("2015-03-30", "-2.769305"));
        Map<String, List<V>> valuesMap1 = new HashMap<>();
        valuesMap1.put("EQ9AK", values1);

        Map<String, Map<String, String>> metaData1 = new HashMap<>();
        Map<String, String> metaValues1 = new HashMap<>();
        metaData1.put("EQ9AK", metaValues1);
        metaValues1.put("reportPeriod", "Q1");
        metaValues1.put("reportType", "R");
        metaValues1.put("numberOfMonths", "3");

        TimeSeriesResult ts1 = new TimeSeriesResult("123", valuesMap1, metaData1);

        List<V> values2 = new ArrayList<>();
        values2.add(new V("2015-06-29", "-4.218763"));
        Map<String, List<V>> valuesMap2 = new HashMap<>();
        valuesMap2.put("EQ9AK", values2);

        Map<String, Map<String, String>> metaData2 = new HashMap<>();
        Map<String, String> metaValues2 = new HashMap<>();
        metaValues2.put("reportPeriod", "Q2");
        metaValues2.put("reportType", "R");
        metaValues2.put("numberOfMonths", "3");
        metaData2.put("EQ9AK", metaValues2);

        TimeSeriesResult ts2 = new TimeSeriesResult("123", valuesMap2, metaData2);

        Mono<InvestmentResponse> investmentResponseMono = new FormatConverter()
                .formatInvestmentResponse(Flux.fromIterable(Arrays.asList(ts1, ts2)));
        InvestmentResponse response = investmentResponseMono.block();
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    assertEquals(2, investmentResponse.getInvestments().get(0).getTimeseriesDataList().size());

                    investmentResponse.getInvestments().get(0).getTimeseriesDataList().forEach(t -> {
                        assertEquals(1, t.getTimeseriesPairList().size());
                        if(t.getTimeseriesPairList().get(0).getDate().equals("2015-03-30")) {
                            assertEquals("-2.769305", t.getTimeseriesPairList().get(0).getValue());
                            assertEquals("Q1", t.getPrimaryKeys().get("reportPeriod"));
                            assertEquals("R", t.getPrimaryKeys().get("reportType"));
                            assertEquals("3", t.getPrimaryKeys().get("numberOfMonths"));
                        } else if(t.getTimeseriesPairList().get(0).getDate().equals("2015-06-29")) {
                            assertEquals("-4.218763", t.getTimeseriesPairList().get(0).getValue());
                            assertEquals("Q2", t.getPrimaryKeys().get("reportPeriod"));
                            assertEquals("R", t.getPrimaryKeys().get("reportType"));
                            assertEquals("3", t.getPrimaryKeys().get("numberOfMonths"));
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(10)
    public void testFormatInvestmentResponseTimeSeriesMultipleValues() {
        Map<String, List<V>> valueMap = new HashMap<>();
        List<V> values = List.of(new V("2024-05-31", "18.64030"), new V("2024-05-31", "27.45645"), new V("2022-04-31;2024-05-31", "12.33268"), new V("2024-04-30", "10.26842"));
        valueMap.put("UB025", values);

        TimeSeriesResult result = new TimeSeriesResult("F00000MLKR", valueMap);
        result.setMultipleValues(true);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> investmentResponseMono =
                formatConverter.formatInvestmentResponse(Flux.fromIterable(List.of(result)), Map.of("UB025", "UB025"));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("F00000MLKR".equals(investment.getId())) {
                            assertEquals(1, investment.getTimeseriesDataList().size(), "result size");
                            List<TimeseriesPair> tsPairList = investment.getTimeseriesDataList().get(0).getTimeseriesPairList();
                            assertEquals(null, tsPairList.get(0).getDate(), "date");
                            assertEquals("2024-05-31", tsPairList.get(0).getEndDate(), "endDate");
                            assertEquals("2024-05-31", tsPairList.get(1).getDate(), "date");
                            assertEquals("2024-04-30", tsPairList.get(2).getDate(), "date");
                            assertEquals(null, tsPairList.get(2).getEndDate(), "endDate");

                            assertEquals(2, tsPairList.get(1).getMultipleValueDataEntries().size(), "multipleValues");
                            assertEquals("18.64030", tsPairList.get(1).getMultipleValueDataEntries().get(0).getValue(), "value");
                            assertEquals("27.45645", tsPairList.get(1).getMultipleValueDataEntries().get(1).getValue(), "value");

                            assertEquals(1, tsPairList.get(2).getMultipleValueDataEntries().size(), "multipleValues");
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(11)
    public void testFormatInvestmentResponseTimeSeriesMultipleValuesCodeMapping() {
        Map<String, Map<String, String>> codeMappings = new HashMap<>();
        codeMappings.put("UB025", Map.of("5", "Bronze", "6", "Silver", "7", "Gold"));
        CodeMappings.setCodeMappings(codeMappings);

        Map<String, List<V>> valueMap = new HashMap<>();
        List<V> values = List.of(new V("2024-05-31", "5"), new V("2024-05-31", "7"));
        valueMap.put("UB025", values);

        TimeSeriesResult result = new TimeSeriesResult("F00000MLKR", valueMap);
        result.setMultipleValues(true);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> investmentResponseMono =
                formatConverter.formatInvestmentResponse(Flux.fromIterable(List.of(result)), Map.of("UB025", "UB025"));
        StepVerifier.create(investmentResponseMono)
                .consumeNextWith(investmentResponse -> {
                    assertEquals(1, investmentResponse.getInvestments().size(), "investment size equals");
                    investmentResponse.getInvestments().forEach(investment -> {
                        if ("F00000MLKR".equals(investment.getId())) {
                            assertEquals(1, investment.getTimeseriesDataList().size(), "result size");
                            List<TimeseriesPair> tsPairList = investment.getTimeseriesDataList().get(0).getTimeseriesPairList();
                            assertEquals("5", tsPairList.get(0).getMultipleValueDataEntries().get(0).getCode(), "code");
                            assertEquals("Gold", tsPairList.get(0).getMultipleValueDataEntries().get(1).getValue(), "value");

                            assertEquals(2, tsPairList.get(0).getMultipleValueDataEntries().size(), "multipleValues");
                        }
                    });
                })
                .verifyComplete();
    }

    @Test
    @Order(12)
    public void testFormatInvestmentResponseTimeSeriesGroup() {

        GroupDataTS group1 = new GroupDataTS(List.of(new TSGroupResultValue("DPW3O", "80.83500")));
        GroupDataTS group2 = new GroupDataTS(List.of(new TSGroupResultValue("DPW3O", "94.06500")));

        TimeseriesGroupData tsGroupData = new TimeseriesGroupData("2024-04-30", List.of(group1, group2));

        TSDatapointValueGroup datapointValueGroup = new TSDatapointValueGroup("YRA9Y_TSAlias", List.of(tsGroupData));

        Map<String, List<TSDatapointValueGroup>> valueMap = new HashMap<>();
        valueMap.put("YRA9Y_TSAlias", List.of(datapointValueGroup));

        TimeseriesGroupResult groupResult = new TimeseriesGroupResult("0C000006T6", valueMap);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> responseMono = formatConverter.formatInvestmentResponse(
                Flux.fromIterable(List.of(groupResult)),
                Map.of("YRA9Y_TSAlias", "YRA9Y_TSAlias")
        );

        StepVerifier.create(responseMono)
                .consumeNextWith(investmentResponse -> {
                    List<Investment> investments = investmentResponse.getInvestments();
                    assertEquals(1, investments.size(), "Investment size equals");

                    Investment investment = investments.get(0);
                    assertEquals("0C000006T6", investment.getId());

                    List<TSDatapointValueGroup> datapoints = investment.getTimeseriesGroupList();
                    assertEquals(1, datapoints.size(), "Datapoint size equals");

                    TSDatapointValueGroup datapoint = datapoints.get(0);
                    assertEquals("YRA9Y_TSAlias", datapoint.getDatapointId());

                    List<TimeseriesGroupData> tsDataList = datapoint.getTimeSeriesData();
                    assertEquals(1, tsDataList.size());

                    TimeseriesGroupData tsData = tsDataList.get(0);
                    assertEquals("2024-04-30", tsData.getDate());

                    List<GroupDataTS> groupData = tsData.getGroupData();
                    assertEquals(2, groupData.size());

                    TSGroupResultValue val1 = groupData.get(0).getValues().get(0);
                    TSGroupResultValue val2 = groupData.get(1).getValues().get(0);

                    assertEquals("DPW3O", val1.getDatapointId());
                    assertEquals("80.83500", val1.getValue());

                    assertEquals("DPW3O", val2.getDatapointId());
                    assertEquals("94.06500", val2.getValue());
                })
                .verifyComplete();
    }

    @Test
    @Order(13)
    public void testHandleTsProtoDataWithValidData() {

        TsDataProtoBuf.TSDataDouble.Builder tsDataBuilder = TsDataProtoBuf.TSDataDouble.newBuilder();
        tsDataBuilder.addDates(18628L); // 2021-01-01 (days since epoch 1970-01-01)
        tsDataBuilder.addDates(18659L); // 2021-02-01 (days since epoch 1970-01-01)
        tsDataBuilder.addDates(18687L); // 2021-03-01 (days since epoch 1970-01-01)
        tsDataBuilder.addValues(100.50);
        tsDataBuilder.addValues(105.75);
        tsDataBuilder.addValues(98.25);
        TsDataProtoBuf.TSDataDouble tsData = tsDataBuilder.build();

        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("TEST_DP_001", tsData);

        TsResult tsRet = new TsResult("INVESTMENT_ID_001", values);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> responseMono = formatConverter.formatInvestmentResponse(
                Flux.fromIterable(List.of(tsRet))
        );

        StepVerifier.create(responseMono)
                .consumeNextWith(invResponse -> {
                    assertEquals(1, invResponse.getInvestments().size(), "Investment size should be 1");

                    Investment investment = invResponse.getInvestments().get(0);
                    assertEquals("INVESTMENT_ID_001", investment.getId());
                    assertEquals(1, investment.getTimeseriesDataList().size(), "Timeseries data list size should be 1");

                    TimeseriesData timeseriesData = investment.getTimeseriesDataList().get(0);
                    assertEquals("TEST_DP_001", timeseriesData.getDatapointId());
                    assertEquals(3, timeseriesData.getTimeseriesPairList().size(), "Should have 3 timeseries pairs");

                    List<TimeseriesPair> pairs = timeseriesData.getTimeseriesPairList();

                    // Verify first pair
                    assertEquals("2021-01-01", pairs.get(0).getDate());
                    assertEquals("100.5", pairs.get(0).getValue());

                    // Verify second pair
                    assertEquals("2021-02-01", pairs.get(1).getDate());
                    assertEquals("105.75", pairs.get(1).getValue());

                    // Verify third pair
                    assertEquals("2021-03-01", pairs.get(2).getDate());
                    assertEquals("98.25", pairs.get(2).getValue());
                })
                .verifyComplete();
    }

    @Test
    @Order(14)
    public void testHandleTsProtoDataWithEmptyValues() {
        Map<String, TsDataProtoBuf.TSDataDouble> emptyValues = new HashMap<>();
        TsResult tsRet = new TsResult("INVESTMENT_ID_002", emptyValues);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> responseMono = formatConverter.formatInvestmentResponse(
                Flux.fromIterable(List.of(tsRet))
        );

        StepVerifier.create(responseMono)
                .consumeNextWith(invResponse -> {
                    assertEquals(1, invResponse.getInvestments().size(), "Investment size should be 1");

                    Investment investment = invResponse.getInvestments().get(0);
                    assertEquals("INVESTMENT_ID_002", investment.getId());
                    assertEquals(0, investment.getTimeseriesDataList().size(), "Timeseries data list should be empty");
                })
                .verifyComplete();
    }


    @Test
    @Order(16)
    public void testHandleTsProtoDataWithDateValueCountMismatch() {
        // Create TSDataDouble with mismatched dates and values count
        TsDataProtoBuf.TSDataDouble.Builder tsDataBuilder = TsDataProtoBuf.TSDataDouble.newBuilder();
        tsDataBuilder.addDates(18628L); // 2021-01-01 (days since epoch 1970-01-01)
        tsDataBuilder.addDates(18659L); // 2021-02-01 (days since epoch 1970-01-01)
        tsDataBuilder.addValues(100.50); // Only one value for two dates
        TsDataProtoBuf.TSDataDouble tsData = tsDataBuilder.build();

        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("TEST_DP_MISMATCH", tsData);

        TsResult tsResult = new TsResult("INVESTMENT_ID_004", values);

        FormatConverter formatConverter = new FormatConverter();
        Mono<InvestmentResponse> responseMono = formatConverter.formatInvestmentResponse(
                Flux.fromIterable(List.of(tsResult))
        );

        StepVerifier.create(responseMono)
                .expectErrorMatches(throwable ->
                    throwable instanceof RuntimeException &&
                    throwable.getMessage().contains("Dates and values count mismatch for datapoint TEST_DP_MISMATCH"))
                .verify();
    }


}
