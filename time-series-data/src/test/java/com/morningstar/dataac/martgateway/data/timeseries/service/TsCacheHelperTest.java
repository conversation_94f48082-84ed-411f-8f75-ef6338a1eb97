package com.morningstar.dataac.martgateway.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import com.morningstar.dataac.martgateway.core.common.util.Lz4Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TsCacheHelperTest {

    @Mock
    private RedisTsRepo redisTsRepo;

    private TsCacheHelper tsCacheHelper;

    @BeforeEach
    void setUp() {
        tsCacheHelper = new TsCacheHelper(redisTsRepo);
    }

    @Test
    void testGetData_WithValidInput_ReturnsFilteredData() {
        // Arrange
        List<String> ids = List.of("INV001", "INV002");
        List<String> dps = List.of("DP001", "DP002");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        // Create test TSDataDouble
        TsDataProtoBuf.TSDataDouble testData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(start.toEpochDay())
                .addDates(start.plusMonths(6).toEpochDay())
                .addDates(end.toEpochDay())
                .addValues(100.0)
                .addValues(150.0)
                .addValues(200.0)
                .addCopyOverDateIndices(1)
                .build();

        byte[] compressedData = "compressed_test_data".getBytes();
        byte[] decompressedData = testData.toByteArray();

        // Mock dependencies
        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenReturn(decompressedData);

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectNextMatches(data -> {
                        assertEquals("INV001", data.getInvestmentId());
                        assertEquals("DP001", data.getDpId());
                        assertEquals(3, data.getDatesList().size());
                        assertEquals(3, data.getValuesList().size());
                        return true;
                    })
                    .expectNextCount(1)
                    .thenCancel()
                    .verify();
        }
    }

    @Test
    void testGetData_WithEmptyIds_ReturnsEmptyFlux() {
        // Arrange
        List<String> ids = List.of();
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        // Act & Assert
        StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                .expectComplete()
                .verify();
    }

    @Test
    void testGetData_WithDecompressionError_ReturnsEmptyFlux() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        byte[] compressedData = "invalid_compressed_data".getBytes();

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenThrow(new RuntimeException("Decompression failed"));

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectComplete()
                    .verify();
        }
    }

    @Test
    void testGetData_WithMultipleYears_GeneratesCorrectRedisKeys() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2022, 1, 1); // Different decade
        LocalDate end = LocalDate.of(2024, 12, 31);

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.empty());

        // Act
        StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                .expectComplete()
                .verify();

        // Assert - Verify correct Redis keys are called
        verify(redisTsRepo, atLeastOnce()).getHashValue(
                argThat(keyBytes -> {
                    String key = new String(keyBytes);
                    return key.startsWith("ts:TEST:INV001:") &&
                           (key.endsWith(":2020") || key.endsWith(":2030")); // 10-year periods
                }),
                anyList()
        );
    }

    @Test
    void testFilterDateRange_WithDataWithinRange_ReturnsAllData() {
        // This tests the private filterDateRange method indirectly through getData
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        TsDataProtoBuf.TSDataDouble testData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(start.toEpochDay())
                .addDates(start.plusMonths(6).toEpochDay())
                .addDates(end.toEpochDay())
                .addValues(100.0)
                .addValues(150.0)
                .addValues(200.0)
                .build();

        byte[] compressedData = "compressed_test_data".getBytes();
        byte[] decompressedData = testData.toByteArray();

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenReturn(decompressedData);

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectNextMatches(data -> data.getDatesList().size() == 3)
                    .thenCancel()
                    .verify();
        }
    }

    @Test
    void testFilterDateRange_WithDataOutsideRange_FiltersCorrectly() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 6, 1);
        LocalDate end = LocalDate.of(2023, 9, 30);

        TsDataProtoBuf.TSDataDouble testData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(LocalDate.of(2023, 1, 1).toEpochDay()) // Before range
                .addDates(LocalDate.of(2023, 7, 15).toEpochDay()) // Within range
                .addDates(LocalDate.of(2023, 12, 31).toEpochDay()) // After range
                .addValues(100.0)
                .addValues(150.0)
                .addValues(200.0)
                .addCopyOverDateIndices(0)
                .addCopyOverDateIndices(2)
                .build();

        byte[] compressedData = "compressed_test_data".getBytes();
        byte[] decompressedData = testData.toByteArray();

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenReturn(decompressedData);

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectNextMatches(data -> {
                        // Should only have 1 date within range
                        assertEquals(1, data.getDatesList().size());
                        assertEquals(LocalDate.of(2023, 7, 15).toEpochDay(),
                                   data.getDatesList().get(0).longValue());
                        assertEquals(1, data.getValuesList().size());
                        assertEquals(150.0, data.getValuesList().get(0), 0.001);
                        // CopyOver indices should be adjusted
                        assertEquals(0, data.getCopyOverDateIndicesList().size());
                        return true;
                    })
                    .thenCancel()
                    .verify();
        }
    }

    @Test
    void testFilterDateRange_WithEmptyData_ReturnsEmptyData() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        TsDataProtoBuf.TSDataDouble testData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .build();

        byte[] compressedData = "compressed_test_data".getBytes();
        byte[] decompressedData = testData.toByteArray();

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenReturn(decompressedData);

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectNextMatches(data -> {
                        assertEquals(0, data.getDatesList().size());
                        assertEquals(0, data.getValuesList().size());
                        assertEquals(0, data.getCopyOverDateIndicesList().size());
                        return true;
                    })
                    .thenCancel()
                    .verify();
        }
    }

    @Test
    void testGetData_WithRedisError_HandlesGracefully() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.error(new RuntimeException("Redis connection error")));

        // Act & Assert
        StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                .expectError(RuntimeException.class)
                .verify();
    }

    @Test
    void testGetData_WithLargeDataSet_HandlesEfficiently() {
        // Arrange
        List<String> ids = List.of("INV001");
        List<String> dps = List.of("DP001");
        String groupPrefix = "TEST";
        LocalDate start = LocalDate.of(2020, 1, 1);
        LocalDate end = LocalDate.of(2023, 12, 31);

        // Create large dataset
        TsDataProtoBuf.TSDataDouble.Builder builder = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001");

        // Add 1000 data points
        for (int i = 0; i < 1000; i++) {
            builder.addDates(start.plusDays(i).toEpochDay());
            builder.addValues(100.0 + i);
            if (i % 10 == 0) {
                builder.addCopyOverDateIndices(i);
            }
        }

        TsDataProtoBuf.TSDataDouble testData = builder.build();
        byte[] compressedData = "compressed_test_data".getBytes();
        byte[] decompressedData = testData.toByteArray();

        when(redisTsRepo.getHashValue(any(byte[].class), anyList()))
                .thenReturn(Flux.just(compressedData));

        try (MockedStatic<Lz4Util> lz4UtilMock = mockStatic(Lz4Util.class)) {
            lz4UtilMock.when(() -> Lz4Util.decompress(compressedData))
                    .thenReturn(decompressedData);

            // Act & Assert
            StepVerifier.create(tsCacheHelper.getData(ids, dps, groupPrefix, start, end))
                    .expectNextMatches(data -> {
                        assertTrue(data.getDatesList().size() <= 1000);
                        assertEquals(data.getDatesList().size(), data.getValuesList().size());
                        return true;
                    })
                    .thenCancel()
                    .verify();
        }
    }


    @Test
    void testGetData_WithNullInputs_HandlesGracefully() {
        // Act & Assert - Test null inputs
        assertThrows(NullPointerException.class, () ->
            tsCacheHelper.getData(null, List.of("DP001"), "TEST",
                               LocalDate.now(), LocalDate.now()).blockFirst());

        assertThrows(NullPointerException.class, () ->
            tsCacheHelper.getData(List.of("INV001"), null, "TEST",
                               LocalDate.now(), LocalDate.now()).blockFirst());
    }
}
