package com.morningstar.dataac.martgateway.data.timeseries.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.IDataPoint;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPoint;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPointGroup;
import com.morningstar.dataac.martgateway.data.timeseries.service.TsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TsGatewayTest {

    @Mock
    private TsService tsService;

    @Mock
    private MartRequest martRequest;

    @Mock
    private IdMapper idMapper1;

    @Mock
    private IdMapper idMapper2;

    @Mock
    private TsDataPoint tsDataPoint1;

    @Mock
    private TsDataPoint tsDataPoint2;

    @Mock
    private TsDataPointGroup tsDataPointGroup1;

    @Mock
    private TsDataPointGroup tsDataPointGroup2;

    private TsGateway tsGateway;

    @BeforeEach
    void setUp() {
        tsGateway = new TsGateway(tsService);
    }

    @Test
    void testRetrieve_WithValidRequest_ReturnsResults() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001", "INV002"));
        when(martRequest.getDps()).thenReturn(List.of("DP001", "DP002"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1, idMapper2));

        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId(anyString())).thenReturn("INV001");

        // Mock DataPoint setup
        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("INVESTMENT_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        when(tsDataPointGroup2.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPoint2.getTsDataPointGroup()).thenReturn(tsDataPointGroup2);

        // Create test TsResult - only expect one result since both datapoints are in same group
        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("DP001", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(LocalDate.of(2023, 1, 1).toEpochDay())
                .addValues(100.0)
                .build());

        TsResult tsResult = new TsResult("INV001", values);

        when(tsService.getData(anyList(), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(tsResult));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);
            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP002", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint2);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectNextMatches(result -> {
                        assertTrue(result instanceof TsResult);
                        TsResult tsRes = (TsResult) result;
                        assertEquals("INV001", tsRes.getId());
                        return true;
                    })
                    .expectComplete()
                    .verify();
        }
    }

    @Test
    void testRetrieve_WithEmptyIds_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getIds()).thenReturn(List.of());

        // Act & Assert
        StepVerifier.create(tsGateway.retrieve(martRequest))
                .expectComplete()
                .verify();
    }

    @Test
    void testRetrieve_WithEmptyDatapoints_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of());

        // Act & Assert
        StepVerifier.create(tsGateway.retrieve(martRequest))
                .expectComplete()
                .verify();
    }

    @Test
    void testRetrieve_WithEmptyStartDate_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("");

        // Act & Assert
        StepVerifier.create(tsGateway.retrieve(martRequest))
                .expectComplete()
                .verify();
    }

    @Test
    void testRetrieve_WithNullStartDate_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn(null);

        // Act & Assert
        StepVerifier.create(tsGateway.retrieve(martRequest))
                .expectComplete()
                .verify();
    }

    @Test
    void testRetrieve_WithInvalidDataPoints_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("INVALID_DP"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class)) {
            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("INVALID_DP", DataPointAggregate.SrcType.TS))
                    .thenReturn(null); // Invalid datapoint returns null

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectComplete()
                    .verify();
        }
    }

    @Test
    void testRetrieve_WithNonTsDataPoint_ReturnsEmptyFlux() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");

        // Return a mock IDataPoint that is NOT a TsDataPoint
        IDataPoint nonTsDataPoint = mock(IDataPoint.class);
        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class)) {
            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(nonTsDataPoint); // Returns non-TsDataPoint object

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectComplete()
                    .verify();
        }
    }

    @Test
    void testRetrieve_WithMultipleGroups_ProcessesEachGroup() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001", "DP002"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1));

        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId(anyString())).thenReturn("INV001");

        // Setup different groups for different datapoints
        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("INVESTMENT_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        when(tsDataPointGroup2.getGroupName()).thenReturn("GROUP_B");
        when(tsDataPointGroup2.getIdLevel()).thenReturn("SECURITY_ID");
        when(tsDataPoint2.getTsDataPointGroup()).thenReturn(tsDataPointGroup2);

        Map<String, TsDataProtoBuf.TSDataDouble> valuesA = new HashMap<>();
        valuesA.put("DP001", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addValues(100.0)
                .build());

        Map<String, TsDataProtoBuf.TSDataDouble> valuesB = new HashMap<>();
        valuesB.put("DP002", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP002")
                .addValues(200.0)
                .build());

        TsResult resultA = new TsResult("INV001", valuesA);
        TsResult resultB = new TsResult("INV001", valuesB);

        when(tsService.getData(eq(List.of("INV001")), eq(List.of("DP001")), eq("GROUP_A"), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(resultA));
        when(tsService.getData(eq(List.of("INV001")), eq(List.of("DP002")), eq("GROUP_B"), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(resultB));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);
            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP002", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint2);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectNextCount(2) // Should get results from both groups
                    .expectComplete()
                    .verify();

            // Verify both groups were processed
            verify(tsService).getData(eq(List.of("INV001")), eq(List.of("DP001")), eq("GROUP_A"), any(LocalDate.class), any(LocalDate.class));
            verify(tsService).getData(eq(List.of("INV001")), eq(List.of("DP002")), eq("GROUP_B"), any(LocalDate.class), any(LocalDate.class));
        }
    }

    @Test
    void testRetrieve_WithIdMapping_ConvertsIds() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1));

        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId("SECURITY_ID")).thenReturn("SEC001"); // Different target ID

        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("SECURITY_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("DP001", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("SEC001")
                .setDpId("DP001")
                .addValues(100.0)
                .build());

        TsResult tsResult = new TsResult("SEC001", values); // Result with target ID

        when(tsService.getData(eq(List.of("SEC001")), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(tsResult));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectNextMatches(result -> {
                        assertTrue(result instanceof TsResult);
                        TsResult tsRes = (TsResult) result;
                        assertEquals("INV001", tsRes.getId()); // Should be converted back to original ID
                        return true;
                    })
                    .expectComplete()
                    .verify();

            // Verify service was called with target ID
            verify(tsService).getData(eq(List.of("SEC001")), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class));
        }
    }

    @Test
    void testRetrieve_WithOneToManyIdMapping_ReturnsMultipleResults() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001", "INV002"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1, idMapper2));

        // Both original IDs map to the same target ID
        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId("SECURITY_ID")).thenReturn("SEC001");
        when(idMapper2.getInvestmentId()).thenReturn("INV002");
        when(idMapper2.getId("SECURITY_ID")).thenReturn("SEC001"); // Same target ID

        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("SECURITY_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("DP001", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("SEC001")
                .setDpId("DP001")
                .addValues(100.0)
                .build());

        TsResult tsResult = new TsResult("SEC001", values);

        when(tsService.getData(eq(List.of("SEC001")), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(tsResult));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectNextMatches(result -> {
                        TsResult tsRes = (TsResult) result;
                        return "INV001".equals(tsRes.getId()) || "INV002".equals(tsRes.getId());
                    })
                    .expectNextMatches(result -> {
                        TsResult tsRes = (TsResult) result;
                        return "INV001".equals(tsRes.getId()) || "INV002".equals(tsRes.getId());
                    })
                    .expectComplete()
                    .verify();
        }
    }

    @Test
    void testRetrieve_WithTsServiceError_PropagatesError() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1));

        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId(anyString())).thenReturn("INV001");

        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("INVESTMENT_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        RuntimeException expectedError = new RuntimeException("Service error");
        when(tsService.getData(anyList(), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.error(expectedError));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectError(RuntimeException.class)
                    .verify();
        }
    }

    @Test
    void testConstructor_WithNullTsService_DoesNotThrow() {
        // Act & Assert
        TsGateway gateway = new TsGateway(null);
        assertNotNull(gateway);
    }

    @Test
    void testRetrieve_WithNullTsService_ThrowsExceptionOnMethodCall() {
        // Arrange
        TsGateway gatewayWithNullService = new TsGateway(null);

        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getIdMappers()).thenReturn(List.of(idMapper1));

        when(idMapper1.getInvestmentId()).thenReturn("INV001");
        when(idMapper1.getId(anyString())).thenReturn("INV001");

        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("INVESTMENT_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate(null))
                    .thenReturn(null);

            // Act & Assert
            StepVerifier.create(gatewayWithNullService.retrieve(martRequest))
                    .expectError(NullPointerException.class)
                    .verify();
        }
    }

    @Test
    void testRetrieve_WithNoIdMappers_UsesOriginalIds() {
        // Arrange
        when(martRequest.getRequestId()).thenReturn("REQ-001");
        when(martRequest.getIds()).thenReturn(List.of("INV001"));
        when(martRequest.getDps()).thenReturn(List.of("DP001"));
        when(martRequest.getStartDate()).thenReturn("2023-01-01");
        when(martRequest.getEndDate()).thenReturn("2023-12-31");
        when(martRequest.getIdMappers()).thenReturn(List.of()); // Empty ID mappers

        when(tsDataPointGroup1.getGroupName()).thenReturn("GROUP_A");
        when(tsDataPointGroup1.getIdLevel()).thenReturn("INVESTMENT_ID");
        when(tsDataPoint1.getTsDataPointGroup()).thenReturn(tsDataPointGroup1);

        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();
        values.put("DP001", TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addValues(100.0)
                .build());

        TsResult tsResult = new TsResult("INV001", values);

        when(tsService.getData(eq(List.of()), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Flux.just(tsResult));

        try (MockedStatic<DataPointRepository> dataPointRepoMock = mockStatic(DataPointRepository.class);
             MockedStatic<DateFormatUtil> dateFormatMock = mockStatic(DateFormatUtil.class)) {

            dataPointRepoMock.when(() -> DataPointRepository.getDpByNid("DP001", DataPointAggregate.SrcType.TS))
                    .thenReturn(tsDataPoint1);

            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-01-01"))
                    .thenReturn(LocalDate.of(2023, 1, 1));
            dateFormatMock.when(() -> DateFormatUtil.parseLocalDate("2023-12-31"))
                    .thenReturn(LocalDate.of(2023, 12, 31));

            // Act & Assert
            StepVerifier.create(tsGateway.retrieve(martRequest))
                    .expectNextMatches(result -> {
                        TsResult tsRes = (TsResult) result;
                        assertEquals("INV001", tsRes.getId());
                        return true;
                    })
                    .expectComplete()
                    .verify();

            // Verify service was called with empty target IDs list
            verify(tsService).getData(eq(List.of()), anyList(), anyString(), any(LocalDate.class), any(LocalDate.class));
        }
    }
}
