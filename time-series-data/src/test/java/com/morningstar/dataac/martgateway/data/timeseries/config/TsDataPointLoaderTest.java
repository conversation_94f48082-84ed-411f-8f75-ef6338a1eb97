package com.morningstar.dataac.martgateway.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.IDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPoint;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TsDataPointLoaderTest {

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @Mock
    private DocumentLoader documentLoader;

    @Mock
    private DataPointLoaderContext context;

    private TsDataPointLoader tsDataPointLoader;
    private Map<String, DataPointAggregate> dataPointAggregateMap;

    @BeforeEach
    void setUp() {
        tsDataPointLoader = new TsDataPointLoader(datapointConfigFileService, documentLoader);
        dataPointAggregateMap = new HashMap<>();
    }

    @Test
    void testProcessDocument_WithSingleDataPointGroup_CreatesCorrectDataPoints() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange
        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID",
                createDataPoint("DP001", "DOUBLE"),
                createDataPoint("DP002", "STRING")
            )
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(2, dataPointAggregateMap.size());
        assertTrue(dataPointAggregateMap.containsKey("DP001"));
        assertTrue(dataPointAggregateMap.containsKey("DP002"));

        // Verify DP001
        DataPointAggregate aggregate1 = dataPointAggregateMap.get("DP001");
        assertEquals("DP001", aggregate1.getId());
        assertTrue(aggregate1.getSrcMap().containsKey(DataPointAggregate.SrcType.TS));

        TsDataPoint tsDataPoint1 = (TsDataPoint) aggregate1.getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("DP001", tsDataPoint1.getNid());
        assertEquals("DOUBLE", tsDataPoint1.getParseFunction());
        assertEquals("GROUP_A", tsDataPoint1.getTsDataPointGroup().getGroupName());
        assertEquals("INVESTMENT_ID", tsDataPoint1.getTsDataPointGroup().getIdLevel());

        // Verify DP002
        DataPointAggregate aggregate2 = dataPointAggregateMap.get("DP002");
        assertEquals("DP002", aggregate2.getId());
        assertTrue(aggregate2.getSrcMap().containsKey(DataPointAggregate.SrcType.TS));

        TsDataPoint tsDataPoint2 = (TsDataPoint) aggregate2.getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("DP002", tsDataPoint2.getNid());
        assertEquals("STRING", tsDataPoint2.getParseFunction());
        assertEquals("GROUP_A", tsDataPoint2.getTsDataPointGroup().getGroupName());
        assertEquals("INVESTMENT_ID", tsDataPoint2.getTsDataPointGroup().getIdLevel());
    }

    @Test
    void testProcessDocument_WithMultipleDataPointGroups_CreatesCorrectStructure() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange
        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID",
                createDataPoint("DP001", "DOUBLE")
            ),
            createDataPointGroup("GROUP_B", "SECURITY_ID",
                createDataPoint("DP002", "STRING"),
                createDataPoint("DP003", "INTEGER")
            )
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(3, dataPointAggregateMap.size());

        // Verify DP001 from GROUP_A
        TsDataPoint tsDataPoint1 = (TsDataPoint) dataPointAggregateMap.get("DP001").getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("GROUP_A", tsDataPoint1.getTsDataPointGroup().getGroupName());
        assertEquals("INVESTMENT_ID", tsDataPoint1.getTsDataPointGroup().getIdLevel());

        // Verify DP002 from GROUP_B
        TsDataPoint tsDataPoint2 = (TsDataPoint) dataPointAggregateMap.get("DP002").getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("GROUP_B", tsDataPoint2.getTsDataPointGroup().getGroupName());
        assertEquals("SECURITY_ID", tsDataPoint2.getTsDataPointGroup().getIdLevel());

        // Verify DP003 from GROUP_B
        TsDataPoint tsDataPoint3 = (TsDataPoint) dataPointAggregateMap.get("DP003").getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("GROUP_B", tsDataPoint3.getTsDataPointGroup().getGroupName());
        assertEquals("SECURITY_ID", tsDataPoint3.getTsDataPointGroup().getIdLevel());
    }

    @Test
    void testProcessDocument_WithExistingDataPointAggregate_AddsToExisting() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange
        String existingNid = "DP001";

        // Create existing DataPointAggregate with different source type
        Map<DataPointAggregate.SrcType, IDataPoint> existingSrcMap = new HashMap<>();
        IDataPoint existingDataPoint = mock(IDataPoint.class);
        existingSrcMap.put(DataPointAggregate.SrcType.EOD, existingDataPoint);

        DataPointAggregate existingAggregate = DataPointAggregate.builder()
                .id(existingNid)
                .srcMap(existingSrcMap)
                .build();

        dataPointAggregateMap.put(existingNid, existingAggregate);

        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID",
                createDataPoint("DP001", "DOUBLE")
            )
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(1, dataPointAggregateMap.size());
        DataPointAggregate aggregate = dataPointAggregateMap.get("DP001");

        // Should have both EOD and TS source types
        assertEquals(2, aggregate.getSrcMap().size());
        assertTrue(aggregate.getSrcMap().containsKey(DataPointAggregate.SrcType.EOD));
        assertTrue(aggregate.getSrcMap().containsKey(DataPointAggregate.SrcType.TS));

        // Verify TS data point was added correctly
        TsDataPoint tsDataPoint = (TsDataPoint) aggregate.getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("DP001", tsDataPoint.getNid());
        assertEquals("DOUBLE", tsDataPoint.getParseFunction());
    }

    @Test
    void testProcessDocument_WithEmptyDocument_DoesNotCreateDataPoints() {
        // Arrange
        Document document = DocumentHelper.createDocument();
        document.addElement("root");

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(0, dataPointAggregateMap.size());
    }

    @Test
    void testProcessDocument_WithEmptyDataPointGroup_DoesNotCreateDataPoints() {
        // Arrange
        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID") // No datapoints
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(0, dataPointAggregateMap.size());
    }

    @Test
    void testProcessDocument_WithMissingAttributes_HandlesGracefully() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange
        Document document = DocumentHelper.createDocument();
        Element root = document.addElement("root");
        Element groupElement = root.addElement("datapoint-group"); // fixed name
        // Missing group and idLevel attributes

        Element dataPointElement = groupElement.addElement("datapoint"); // fixed name
        dataPointElement.addAttribute("id", "DP001");
        // Missing parseFunction attribute

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(1, dataPointAggregateMap.size());
        DataPointAggregate aggregate = dataPointAggregateMap.get("DP001");
        assertNotNull(aggregate);

        TsDataPoint tsDataPoint = (TsDataPoint) aggregate.getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("DP001", tsDataPoint.getNid());
        assertNull(tsDataPoint.getParseFunction());
        assertNull(tsDataPoint.getTsDataPointGroup().getGroupName());
        assertNull(tsDataPoint.getTsDataPointGroup().getIdLevel());
    }

    @Test
    void testProcessDocument_WithSameDataPointInMultipleGroups_CreatesMultipleInstances() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange - Same datapoint ID in different groups
        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID",
                createDataPoint("DP001", "DOUBLE")
            ),
            createDataPointGroup("GROUP_B", "SECURITY_ID",
                createDataPoint("DP002", "STRING") // Same ID, different group
            )
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        assertEquals(2, dataPointAggregateMap.size()); // Still one aggregate
        DataPointAggregate aggregate = dataPointAggregateMap.get("DP001");

        // Should only have TS source type (second one overwrites first)
        assertEquals(1, aggregate.getSrcMap().size());
        assertTrue(aggregate.getSrcMap().containsKey(DataPointAggregate.SrcType.TS));

        // The last processed datapoint should be the one that remains
        TsDataPoint tsDataPoint = (TsDataPoint) aggregate.getSrcMap().get(DataPointAggregate.SrcType.TS);
        assertEquals("DP001", tsDataPoint.getNid());
        assertEquals("DOUBLE", tsDataPoint.getParseFunction());
        assertEquals("GROUP_A", tsDataPoint.getTsDataPointGroup().getGroupName());
        assertEquals("INVESTMENT_ID", tsDataPoint.getTsDataPointGroup().getIdLevel());
    }

    @Test
    void testConstructor_WithValidParameters_CreatesInstance() {
        // Act
        TsDataPointLoader loader = new TsDataPointLoader(datapointConfigFileService, documentLoader);

        // Assert
        assertNotNull(loader);
    }

    @Test
    void testProcessDocument_VerifiesContextInteraction() {
        when(context.getDataPointAggregateMap()).thenReturn(dataPointAggregateMap);
        // Arrange
        Document document = createTestDocument(
            createDataPointGroup("GROUP_A", "INVESTMENT_ID",
                createDataPoint("DP001", "DOUBLE")
            )
        );

        // Act
        tsDataPointLoader.processDocument(context, document);

        // Assert
        verify(context, atLeastOnce()).getDataPointAggregateMap();
    }

    // Helper methods for creating test documents
    private Document createTestDocument(Element... dataPointGroups) {
        Document document = DocumentHelper.createDocument();
        Element root = document.addElement("root");

        for (Element group : dataPointGroups) {
            root.add(group.createCopy());
        }

        return document;
    }

    private Element createDataPointGroup(String groupName, String idLevel, Element... dataPoints) {
        Element groupElement = DocumentHelper.createElement("datapoint-group"); // fixed name
        if (groupName != null) {
            groupElement.addAttribute("group", groupName);
        }
        if (idLevel != null) {
            groupElement.addAttribute("idlevel", idLevel);
        }

        for (Element dataPoint : dataPoints) {
            groupElement.add(dataPoint.createCopy());
        }

        return groupElement;
    }

    private Element createDataPoint(String id, String parseFunction) {
        Element dataPointElement = DocumentHelper.createElement("datapoint"); // fixed name
        if (id != null) {
            dataPointElement.addAttribute("id", id);
        }
        if (parseFunction != null) {
            dataPointElement.addAttribute("parse-function", parseFunction);
        }
        return dataPointElement;
    }
}
