package com.morningstar.dataac.martgateway.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TsServiceImplTest {

    @Mock
    private TsCacheHelper tsCacheHelper;

    private TsServiceImpl tsService;

    @BeforeEach
    void setUp() {
        tsService = new TsServiceImpl(tsCacheHelper);
    }

    @Test
    void testGetData_WithValidInput_ReturnsGroupedResults() {
        // Arrange
        List<String> idList = List.of("INV001", "INV002");
        List<String> dpsList = List.of("DP001", "DP002");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        // Create test data for INV001
        TsDataProtoBuf.TSDataDouble inv001Dp001 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addValues(100.0)
                .build();

        TsDataProtoBuf.TSDataDouble inv001Dp002 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP002")
                .addDates(startDate.toEpochDay())
                .addValues(200.0)
                .build();

        // Create test data for INV002
        TsDataProtoBuf.TSDataDouble inv002Dp001 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV002")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addValues(300.0)
                .build();

        TsDataProtoBuf.TSDataDouble inv002Dp002 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV002")
                .setDpId("DP002")
                .addDates(startDate.toEpochDay())
                .addValues(400.0)
                .build();

        // Mock the tsCacheHelper to return test data
        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.just(inv001Dp001, inv001Dp002, inv002Dp001, inv002Dp002));

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    // Should get one result for INV001
                    assertEquals("INV001", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(2, values.size());
                    assertTrue(values.containsKey("DP001"));
                    assertTrue(values.containsKey("DP002"));
                    assertEquals(100.0, values.get("DP001").getValues(0), 0.001);
                    assertEquals(200.0, values.get("DP002").getValues(0), 0.001);
                    return true;
                })
                .expectNextMatches(result -> {
                    // Should get one result for INV002
                    assertEquals("INV002", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(2, values.size());
                    assertTrue(values.containsKey("DP001"));
                    assertTrue(values.containsKey("DP002"));
                    assertEquals(300.0, values.get("DP001").getValues(0), 0.001);
                    assertEquals(400.0, values.get("DP002").getValues(0), 0.001);
                    return true;
                })
                .expectComplete()
                .verify();

        // Verify that tsCacheHelper was called with correct parameters
        verify(tsCacheHelper).getData(idList, dpsList, groupName, startDate, endDate);
    }

    @Test
    void testGetData_WithSingleInvestment_ReturnsOneResult() {
        // Arrange
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001", "DP002", "DP003");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate endDate = LocalDate.of(2023, 6, 30);

        TsDataProtoBuf.TSDataDouble dp001 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addValues(150.0)
                .build();

        TsDataProtoBuf.TSDataDouble dp002 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP002")
                .addDates(startDate.toEpochDay())
                .addValues(250.0)
                .build();

        TsDataProtoBuf.TSDataDouble dp003 = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP003")
                .addDates(startDate.toEpochDay())
                .addValues(350.0)
                .build();

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.just(dp001, dp002, dp003));

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    assertEquals("INV001", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(3, values.size());
                    assertEquals(150.0, values.get("DP001").getValues(0), 0.001);
                    assertEquals(250.0, values.get("DP002").getValues(0), 0.001);
                    assertEquals(350.0, values.get("DP003").getValues(0), 0.001);
                    return true;
                })
                .expectComplete()
                .verify();
    }

    @Test
    void testGetData_WithEmptyDataFromCache_ReturnsEmptyFlux() {
        // Arrange
        List<String> idList = List.of("INV001", "INV002");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.empty());

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectComplete()
                .verify();

        verify(tsCacheHelper).getData(idList, dpsList, groupName, startDate, endDate);
    }

    @Test
    void testGetData_WithCacheError_PropagatesError() {
        // Arrange
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        RuntimeException expectedError = new RuntimeException("Cache error");
        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.error(expectedError));

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectError(RuntimeException.class)
                .verify();
    }

    @Test
    void testGetData_WithPartialData_OnlyReturnsInvestmentsWithData() {
        // Arrange - Only INV001 has data, INV002 has none
        List<String> idList = List.of("INV001", "INV002");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        TsDataProtoBuf.TSDataDouble inv001Data = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addValues(100.0)
                .build();

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.just(inv001Data)); // Only INV001 data

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    assertEquals("INV001", result.getId());
                    assertEquals(1, result.getValues().size());
                    return true;
                })
                .expectComplete() // Should not get a result for INV002
                .verify();
    }

    @Test
    void testGetData_WithDuplicateDataPointsForSameInvestment_OverwritesPrevious() {
        // Arrange - Same investment ID with duplicate datapoint IDs
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        TsDataProtoBuf.TSDataDouble firstData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addValues(100.0)
                .build();

        TsDataProtoBuf.TSDataDouble duplicateData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001") // Same datapoint ID
                .addDates(startDate.plusDays(1).toEpochDay())
                .addValues(200.0)
                .build();

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.just(firstData, duplicateData));

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    assertEquals("INV001", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(1, values.size());
                    // Should contain the last emitted data for DP001
                    TsDataProtoBuf.TSDataDouble finalData = values.get("DP001");
                    assertEquals(200.0, finalData.getValues(0), 0.001);
                    return true;
                })
                .expectComplete()
                .verify();
    }

    @Test
    void testGetData_WithMultipleDataPointsPerInvestment_GroupsCorrectly() {
        // Arrange
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001", "DP002", "DP003", "DP004", "DP005");
        String groupName = "LARGE_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        // Create 5 different datapoints for the same investment
        Flux<TsDataProtoBuf.TSDataDouble> testData = Flux.range(1, 5)
                .map(i -> TsDataProtoBuf.TSDataDouble.newBuilder()
                        .setInvestmentId("INV001")
                        .setDpId("DP00" + i)
                        .addDates(startDate.toEpochDay())
                        .addValues(i * 100.0)
                        .build());

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(testData);

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    assertEquals("INV001", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(5, values.size());

                    for (int i = 1; i <= 5; i++) {
                        String dpId = "DP00" + i;
                        assertTrue(values.containsKey(dpId), "Missing datapoint: " + dpId);
                        assertEquals(i * 100.0, values.get(dpId).getValues(0), 0.001);
                    }
                    return true;
                })
                .expectComplete()
                .verify();
    }

    @Test
    void testGetData_WithComplexTimeSeriesData_PreservesAllData() {
        // Arrange
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        // Create complex time series data with multiple dates and copy-over indices
        TsDataProtoBuf.TSDataDouble complexData = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId("INV001")
                .setDpId("DP001")
                .addDates(startDate.toEpochDay())
                .addDates(startDate.plusMonths(3).toEpochDay())
                .addDates(startDate.plusMonths(6).toEpochDay())
                .addDates(startDate.plusMonths(9).toEpochDay())
                .addValues(100.0)
                .addValues(110.0)
                .addValues(120.0)
                .addValues(130.0)
                .addCopyOverDateIndices(1)
                .addCopyOverDateIndices(3)
                .build();

        when(tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate))
                .thenReturn(Flux.just(complexData));

        // Act & Assert
        StepVerifier.create(tsService.getData(idList, dpsList, groupName, startDate, endDate))
                .expectNextMatches(result -> {
                    assertEquals("INV001", result.getId());
                    Map<String, TsDataProtoBuf.TSDataDouble> values = result.getValues();
                    assertEquals(1, values.size());

                    TsDataProtoBuf.TSDataDouble retrievedData = values.get("DP001");
                    assertNotNull(retrievedData);
                    assertEquals(4, retrievedData.getDatesList().size());
                    assertEquals(4, retrievedData.getValuesList().size());
                    assertEquals(2, retrievedData.getCopyOverDateIndicesList().size());

                    // Verify the data integrity is preserved
                    assertEquals(100.0, retrievedData.getValues(0), 0.001);
                    assertEquals(110.0, retrievedData.getValues(1), 0.001);
                    assertEquals(120.0, retrievedData.getValues(2), 0.001);
                    assertEquals(130.0, retrievedData.getValues(3), 0.001);

                    return true;
                })
                .expectComplete()
                .verify();
    }

    @Test
    void testConstructor_WithNullCacheHelper_DoesNotThrow() {
        // Act & Assert - Constructor doesn't validate null
        TsServiceImpl service = new TsServiceImpl(null);
        assertNotNull(service);
    }

    @Test
    void testGetData_WithNullCacheHelper_ThrowsExceptionOnMethodCall() {
        // Arrange
        TsServiceImpl serviceWithNullHelper = new TsServiceImpl(null);
        List<String> idList = List.of("INV001");
        List<String> dpsList = List.of("DP001");
        String groupName = "TEST_GROUP";
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);

        // Act & Assert
        assertThrows(NullPointerException.class, () ->
            serviceWithNullHelper.getData(idList, dpsList, groupName, startDate, endDate).blockFirst());
    }

    @Test
    void testGetData_WithNullParameters_PassesToCacheHelper() {
        // Arrange - Test that null parameters are passed through to cache helper
        when(tsCacheHelper.getData(isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(Flux.empty());

        // Act & Assert
        StepVerifier.create(tsService.getData(null, null, null, null, null))
                .expectComplete()
                .verify();

        verify(tsCacheHelper).getData(null, null, null, null, null);
    }
}
