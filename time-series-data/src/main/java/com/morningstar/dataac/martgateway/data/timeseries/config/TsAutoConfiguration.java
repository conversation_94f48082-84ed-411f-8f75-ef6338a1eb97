package com.morningstar.dataac.martgateway.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointAutoConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.config.EnableDataPointLoaders;
import com.morningstar.dataac.martgateway.data.timeseries.service.TsCacheHelper;
import com.morningstar.dataac.martgateway.data.timeseries.service.TsService;
import com.morningstar.dataac.martgateway.data.timeseries.service.TsServiceImpl;
import com.morningstar.dataac.martgateway.data.timeseries.gateway.TsGateway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AutoConfigureAfter(DataPointAutoConfiguration.class)
@ConditionalOnProperty(name = "martgateway.modules.timeseries.enable", havingValue = "true", matchIfMissing = false)
@EnableDataPointLoaders
public class TsAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(TsCacheHelper.class)
    public TsCacheHelper tsCacheHelper(@Qualifier("tsRepo") RedisTsRepo redisTsRepo) {
        return new TsCacheHelper(redisTsRepo);
    }

    @Bean
    @ConditionalOnMissingBean(TsService.class)
    public TsService tsService(TsCacheHelper tsCacheHelper) {
        return new TsServiceImpl(tsCacheHelper);
    }

    @Bean(name = "NewTsGateway")
    @ConditionalOnMissingBean(TsGateway.class)
    public TsGateway tsGateway(TsService tsService) {
        return new TsGateway(tsService);
    }
}
