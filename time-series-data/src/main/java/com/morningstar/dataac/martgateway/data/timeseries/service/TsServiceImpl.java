package com.morningstar.dataac.martgateway.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.List;

@Slf4j
public class TsServiceImpl implements TsService {
    private final TsCacheHelper tsCacheHelper;

    public TsServiceImpl(TsCacheHelper tsCacheHelper) {
        this.tsCacheHelper = tsCacheHelper;
    }

    @Override
    public Flux<TsResult> getData(List<String> idList, List<String> dpsList, String groupName, LocalDate startDate, LocalDate endDate) {
        return tsCacheHelper.getData(idList, dpsList, groupName, startDate, endDate)
                .groupBy(TsDataProtoBuf.TSDataDouble::getInvestmentId) // Group by investment ID
                .flatMap(groupedFlux -> groupedFlux.collectMap(
                        TsDataProtoBuf.TSDataDouble::getDpId,  // Key: datapoint ID
                    tsData -> tsData             // Value: TSDataDouble
                ).map(valuesMap -> {
                    String investmentId = groupedFlux.key();
                    return new TsResult(investmentId, valuesMap);
                }));
    }
}
