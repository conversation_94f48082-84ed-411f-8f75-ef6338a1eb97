package com.morningstar.dataac.martgateway.data.timeseries.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.data.timeseries.service.TsService;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class TsGateway implements Gateway<Result, MartRequest> {
    private final TsService tsService;

    // Group to idlevel value is always same so it is safe to use hashmap here for multithreading and hashmap is faster than concurrenthashmap.
    private final Map<String, String> groupToIdLevelMap = new HashMap<>();

    public TsGateway(TsService tsService) {
        this.tsService = tsService;
    }

    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());

        if (CollectionUtils.isEmpty(martRequest.getIds()) || CollectionUtils.isEmpty(martRequest.getDps())
        || StringUtils.isEmpty(martRequest.getStartDate())) {
            return Flux.empty();
        }

        // Group datapoints by group name and extract ID levels
        Map<String, List<String>> groupToDpsMap = new HashMap<>();
        buildGroupMappings(martRequest.getDps(), groupToDpsMap);

        if (MapUtils.isEmpty(groupToDpsMap)) {
            return Flux.empty();
        }

        LocalDate start = DateFormatUtil.parseLocalDate(martRequest.getStartDate());
        LocalDate end = DateFormatUtil.parseLocalDate(martRequest.getEndDate());
        // Process each group separately
        return Flux.fromIterable(groupToDpsMap.entrySet())
                .flatMap(entry -> processGroup(entry.getKey(), entry.getValue(),
                    groupToIdLevelMap.get(entry.getKey()), martRequest, start, end))
                .subscribeOn(SchedulerConfiguration.getScheduler());
    }

    private void buildGroupMappings(List<String> dpsIds, Map<String, List<String>> groupToDpsMap) {
        for (String dpsId : dpsIds) {
            Object dataPoint = DataPointRepository.getDpByNid(dpsId, DataPointAggregate.SrcType.TS);
            if (dataPoint instanceof TsDataPoint) {
                TsDataPoint tsDataPoint = (TsDataPoint) dataPoint;
                String groupName = tsDataPoint.getTsDataPointGroup().getGroupName();
                groupToDpsMap.computeIfAbsent(groupName, k -> new ArrayList<>()).add(dpsId);
                groupToIdLevelMap.computeIfAbsent(groupName, k -> tsDataPoint.getTsDataPointGroup().getIdLevel());
            }
        }
    }

    private Flux<Result> processGroup(String groupName, List<String> dpsIds, String idLevel,
                                    MartRequest martRequest, LocalDate start, LocalDate end) {
        IdMappingResult idMapping = createIdMapping(martRequest.getIdMappers(), idLevel);
        return tsService.getData(idMapping.getTargetIds(), dpsIds, groupName, start, end)
                .flatMap(tsResult -> convertResultIds(tsResult, idMapping));
    }

    private IdMappingResult createIdMapping(List<IdMapper> idMappers, String idLevel) {
        Map<String, List<String>> targetToOriginalIdsMap = new HashMap<>();
        List<String> targetIds = new ArrayList<>();

        for (IdMapper idMapper : idMappers) {
            String originalId = idMapper.getInvestmentId();
            String targetId = idMapper.getId(idLevel);

            if (targetId != null && !targetId.equals(originalId)) {
                // Need ID conversion
                targetToOriginalIdsMap.computeIfAbsent(targetId, k -> new ArrayList<>()).add(originalId);
                if (!targetIds.contains(targetId)) {
                    targetIds.add(targetId);
                }
            } else {
                // No conversion needed, use original ID
                targetIds.add(originalId);
                targetToOriginalIdsMap.computeIfAbsent(originalId, k -> new ArrayList<>()).add(originalId);
            }
        }

        return new IdMappingResult(targetIds, targetToOriginalIdsMap);
    }

    private Flux<Result> convertResultIds(TsResult tsResult, IdMappingResult idMapping) {
        String targetId = tsResult.getId();
        List<String> originalIds = idMapping.getTargetToOriginalIdsMap().get(targetId);

        if (CollectionUtils.isEmpty(originalIds) || (originalIds.size() == 1 && originalIds.get(0).equals(targetId))) {
            // No conversion needed, return as is
            return Flux.just(tsResult);
        } else {
            // Need to create multiple TsResult for the original IDs
            return Flux.fromIterable(originalIds)
                    .map(originalId -> new TsResult(originalId, tsResult.getValues()));
        }
    }

    private static class IdMappingResult {
        private final List<String> targetIds;
        private final Map<String, List<String>> targetToOriginalIdsMap;

        public IdMappingResult(List<String> targetIds, Map<String, List<String>> targetToOriginalIdsMap) {
            this.targetIds = targetIds;
            this.targetToOriginalIdsMap = targetToOriginalIdsMap;
        }

        public List<String> getTargetIds() {
            return targetIds;
        }

        public Map<String, List<String>> getTargetToOriginalIdsMap() {
            return targetToOriginalIdsMap;
        }
    }
}
